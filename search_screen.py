"""
Online Music Search Screen for Music Player Application
Handles searching for music online and downloading results
"""

import os
import threading
import time
import logging
import json
import re
import requests
from urllib.parse import quote_plus, urlparse
from kivy.properties import StringProperty, BooleanProperty, ListProperty, ObjectProperty
from kivy.clock import Clock, mainthread
from kivy.metrics import dp
from kivymd.uix.screen import MDScreen
from kivymd.uix.list import OneLineAvatarIconListItem, ImageLeftWidget
from kivymd.uix.button import MDIconButton
from kivymd.toast import toast
from kivymd.uix.label import MDLabel
from kivymd.uix.spinner import MDSpinner

# Import Arabic text handling utilities
try:
    from arabic_utils import reshape_arabic_text, contains_arabic, get_display_text, ARABIC_SUPPORT
    logger = logging.getLogger(__name__)
    logger.info("Arabic text utilities imported successfully")
except ImportError as e:
    logger.warning(f"Failed to import Arabic text utilities: {e}")
    # Define fallback functions if the module is not available
    def contains_arabic(text): return False
    def reshape_arabic_text(text): return text
    def get_display_text(text, always_process=False): return text
    ARABIC_SUPPORT = False

# Try to import YouTube libraries
try:
    import yt_dlp
    YTDLP_AVAILABLE = True
except ImportError:
    YTDLP_AVAILABLE = False
    logging.warning("yt-dlp not available. YouTube search may be limited.")

try:
    from pytube import YouTube, Search
    PYTUBE_AVAILABLE = True
except ImportError:
    PYTUBE_AVAILABLE = False
    logging.warning("pytube not available. YouTube search may be limited.")

# At least one of the libraries should be available for YouTube search
YOUTUBE_SEARCH_AVAILABLE = PYTUBE_AVAILABLE or YTDLP_AVAILABLE

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class SearchResultItem(OneLineAvatarIconListItem):
    """Class representing a search result item in the list"""
    title = StringProperty("")
    artist = StringProperty("")
    duration = StringProperty("")
    source = StringProperty("")
    download_url = StringProperty("")
    thumbnail_url = StringProperty("")
    is_playing = BooleanProperty(False)   # Indicates if this item is currently playing
    on_item_click = ObjectProperty(None)  # Callback for item click
    on_download = ObjectProperty(None)    # Callback for download button
    on_stop = ObjectProperty(None)        # Callback for stop button

    def __init__(self, **kwargs):
        # Extract our custom properties
        self.title = kwargs.pop('title', "")
        self.artist = kwargs.pop('artist', "")
        self.duration = kwargs.pop('duration', "")
        self.source = kwargs.pop('source', "")
        self.download_url = kwargs.pop('download_url', "")

        # Ensure thumbnail_url is never None
        thumbnail_url = kwargs.pop('thumbnail_url', "")
        if thumbnail_url is None:
            thumbnail_url = ""
        self.thumbnail_url = thumbnail_url

        self.on_item_click = kwargs.pop('on_item_click', None)
        self.on_download = kwargs.pop('on_download', None)
        self.on_stop = kwargs.pop('on_stop', None)

        # Inicializar la animación del ecualizador
        self._equalizer_event = None

        # Process Arabic text if needed
        display_title = get_display_text(self.title)
        display_artist = get_display_text(self.artist)

        # Set the text property with properly formatted Arabic text if needed
        if 'text' not in kwargs:
            kwargs['text'] = f"{display_title}"

        # Initialize the parent class
        super(SearchResultItem, self).__init__(**kwargs)

        # Apply Arabic text handling to labels after initialization
        self._apply_arabic_text_handling()

        # Add thumbnail image after initialization with improved styling
        try:
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivy.uix.image import AsyncImage
            from kivy.graphics import Color, RoundedRectangle

            # Create a container for the thumbnail with rounded corners
            thumbnail_container = MDBoxLayout(
                size_hint=(None, None),
                size=("56dp", "56dp"),
                padding=("4dp", "4dp", "4dp", "4dp"),
                pos_hint={"center_y": 0.5}
            )

            # Store the rectangle as an attribute of the container
            thumbnail_container.rect = None

            # Add rounded corners to the container
            with thumbnail_container.canvas.before:
                Color(rgba=(0.9, 0.9, 0.9, 1))
                thumbnail_container.rect = RoundedRectangle(
                    pos=thumbnail_container.pos,
                    size=thumbnail_container.size,
                    radius=[10, 10, 10, 10]
                )

            # Bind the rectangle position and size to the container
            def update_rect(instance, value):
                if hasattr(instance, 'rect') and instance.rect:
                    instance.rect.pos = instance.pos
                    instance.rect.size = instance.size

            thumbnail_container.bind(pos=update_rect, size=update_rect)

            # Create the image widget
            image = AsyncImage(
                source=self.thumbnail_url if self.thumbnail_url and self.thumbnail_url.strip() else "images/default_album_cover.png",
                allow_stretch=True,
                keep_ratio=True
            )

            # Add the image to the container
            thumbnail_container.add_widget(image)

            # Add the container to the item
            self.add_widget(thumbnail_container)

        except Exception as e:
            logger.error(f"Error adding thumbnail image: {e}")
            # Fallback to simple image if the fancy one fails
            try:
                image = ImageLeftWidget(
                    source=self.thumbnail_url if self.thumbnail_url and self.thumbnail_url.strip() else "images/default_album_cover.png"
                )
                self.add_widget(image)
            except Exception as e2:
                logger.error(f"Error adding fallback thumbnail image: {e2}")

        # Add buttons on the right side
        try:
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.relativelayout import MDRelativeLayout

            # Create a container for the buttons that will be positioned on the right
            buttons_container = MDBoxLayout(
                orientation='horizontal',
                adaptive_size=True,
                spacing="8dp",
                padding=["0dp", "0dp", "8dp", "0dp"],  # left, top, right, bottom
                pos_hint={"center_y": 0.5, "right": 1},
                size_hint_x=None,
                width="80dp"  # Fixed width for buttons area
            )

            # Get app colors
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            primary_color = app.root.get_primary_color() if app and hasattr(app, 'root') else [0.3, 0.8, 1, 1]

            # Add download button first (on the right)
            if self.on_download:
                download_btn = MDIconButton(
                    icon="download",
                    theme_text_color="Custom",
                    text_color=primary_color,
                    pos_hint={"center_y": 0.5},
                    ripple_scale=1.5,
                    on_release=lambda x: self.on_download() if self.on_download else None
                )
                buttons_container.add_widget(download_btn)

            # Add stop button second (to the left of download button)
            if self.on_stop:
                stop_btn = MDIconButton(
                    icon="stop",
                    theme_text_color="Custom",
                    text_color=[1, 0.3, 0.3, 1],  # Reddish color
                    pos_hint={"center_y": 0.5},
                    ripple_scale=1.5,
                    on_release=lambda x: self.on_stop(self) if self.on_stop else None
                )
                buttons_container.add_widget(stop_btn)

            # Add the container to the item
            self.add_widget(buttons_container)

        except Exception as e:
            logger.error(f"Error adding buttons: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def on_release(self):
        """Called when the item is clicked/released"""
        super(SearchResultItem, self).on_release()
        # Call the callback if it exists
        if self.on_item_click:
            self.on_item_click(self)

    def on_is_playing(self, instance, value):
        """Called when the is_playing property changes"""
        if value:  # Si está reproduciendo
            self.start_equalizer_animation()
        else:  # Si no está reproduciendo
            self.stop_equalizer_animation()

    def start_equalizer_animation(self):
        """Inicia la animación del ecualizador"""
        from kivy.clock import Clock
        from random import uniform

        # Detener cualquier animación previa
        self.stop_equalizer_animation()

        # Función para actualizar la altura de las barras del ecualizador
        def update_equalizer(dt):
            if not self.is_playing:
                return False

            # Actualizar las barras con alturas aleatorias
            with self.canvas.after:
                self.canvas.after.clear()
                from kivy.graphics import Color, Rectangle, Line, RoundedRectangle
                from kivy.metrics import dp

                # Buscar el contenedor del ecualizador
                container = None
                for child in self.children:
                    if hasattr(child, 'id') and child.id == 'equalizer_container':
                        container = child
                        break

                # Si no encontramos el contenedor, usar la posición predeterminada
                if container:
                    base_x = container.x + dp(5)  # Margen izquierdo dentro del contenedor
                    base_y = container.y + dp(5)  # Margen inferior dentro del contenedor
                    width = container.width - dp(10)  # Ancho total menos márgenes
                    height = container.height - dp(10)  # Alto total menos márgenes
                else:
                    # Posición base del ecualizador (encima de la imagen)
                    base_x = self.x + dp(16)  # Alineado con la imagen de portada
                    base_y = self.y + self.height/2 - dp(20)  # Centrado verticalmente
                    width = dp(40)  # Ancho del contenedor
                    height = dp(40)  # Alto del contenedor

                # Número de barras y espacio entre ellas
                num_bars = 5
                bar_width = dp(4)
                spacing = (width - (num_bars * bar_width)) / (num_bars - 1)

                # Get app colors for the equalizer
                from kivymd.app import MDApp
                app = MDApp.get_running_app()
                primary_color = app.root.get_primary_color() if app and hasattr(app, 'root') else [0.3, 0.8, 1, 1]

                # Extract RGB components from primary color
                r, g, b = primary_color[0], primary_color[1], primary_color[2]

                # Dibujar barras del ecualizador con efecto de brillo y usando el color primario
                for i in range(num_bars):
                    # Altura aleatoria para cada barra (entre 20% y 100% del alto disponible)
                    bar_height = uniform(height * 0.2, height * 0.9)

                    # Posición X de cada barra
                    x = base_x + i * (bar_width + spacing)

                    # Posición Y (centrada verticalmente)
                    y = base_y + (height - bar_height) / 2

                    # Dibujar barra principal con el color primario de la app
                    Color(r, g, b, 1)
                    RoundedRectangle(
                        pos=[x, y],
                        size=[bar_width, bar_height],
                        radius=[dp(2)]
                    )

                    # Dibujar brillo en la parte superior (blanco)
                    Color(1.0, 1.0, 1.0, 0.7)
                    RoundedRectangle(
                        pos=[x, y + bar_height - dp(2)],
                        size=[bar_width, dp(2)],
                        radius=[dp(1)]
                    )

                    # Añadir sombra en la parte inferior para efecto 3D
                    Color(0.0, 0.0, 0.0, 0.3)
                    RoundedRectangle(
                        pos=[x, y],
                        size=[bar_width, dp(2)],
                        radius=[dp(1)]
                    )

            return True  # Continuar la animación

        # Programar la actualización cada 0.15 segundos (más rápido para mejor efecto visual)
        self._equalizer_event = Clock.schedule_interval(update_equalizer, 0.15)

    def stop_equalizer_animation(self):
        """Detiene la animación del ecualizador"""
        if self._equalizer_event:
            self._equalizer_event.cancel()
            self._equalizer_event = None

            # Limpiar el canvas
            with self.canvas.after:
                self.canvas.after.clear()

    def _apply_arabic_text_handling(self):
        """Apply Arabic text handling to all labels in this widget"""
        try:
            # Find all MDLabel widgets in this item
            from kivymd.uix.label import MDLabel

            # First handle the main text of the item itself
            if hasattr(self, 'text') and contains_arabic(self.text):
                # Apply bidirectional algorithm to the text if needed
                if not self.text == get_display_text(self.text):
                    self.text = get_display_text(self.text)

                # Set font style for better Arabic display
                self.font_style = "Subtitle1"

                # Set text alignment to right for Arabic text
                if hasattr(self, '_txt_left_pad'):
                    # Adjust padding for right-to-left text
                    original_left = self._txt_left_pad
                    self._txt_left_pad = self._txt_right_pad
                    self._txt_right_pad = original_left

            # Then process all child labels
            for child in self.walk():
                if isinstance(child, MDLabel):
                    # Check if the text contains Arabic characters
                    if hasattr(child, 'text') and child.text and contains_arabic(child.text):
                        # Set text alignment to right for Arabic text
                        child.halign = 'right'

                        # Apply bidirectional algorithm to the text
                        child.text = get_display_text(child.text)

                        # Set font name if available from app
                        from kivymd.app import MDApp
                        app = MDApp.get_running_app()
                        if app and hasattr(app, 'root') and hasattr(app.root, 'current_font'):
                            child.font_name = app.root.current_font

                            # Increase font size slightly for better readability
                            if hasattr(child, 'font_size'):
                                from kivy.metrics import sp
                                child.font_size = sp(16)
        except Exception as e:
            logger.error(f"Error applying Arabic text handling: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class SearchScreen(MDScreen):
    """Screen for searching music online"""
    search_query = StringProperty("")
    is_searching = BooleanProperty(False)
    search_results = ListProperty([])

    def __init__(self, **kwargs):
        super(SearchScreen, self).__init__(**kwargs)
        self.name = "search"
        self.download_manager = None
        logger.debug("SearchScreen initialized")

    def _get_app_font(self):
        """Get the current font from the app"""
        try:
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            if app and hasattr(app, 'root') and hasattr(app.root, 'current_font'):
                return app.root.current_font
        except Exception as e:
            logger.error(f"Error getting app font: {e}")
        return 'NotoNaskhArabic-VariableFont_wght'  # Default font for Arabic

    def on_enter(self):
        """Called when the screen is entered"""
        logger.debug("SearchScreen.on_enter called")
        # Focus the search field
        if hasattr(self.ids, 'search_field'):
            self.ids.search_field.focus = True

        # Ensure the bottom bar is visible if a song is playing
        from kivymd.app import MDApp
        app = MDApp.get_running_app()
        if app and hasattr(app, 'root') and hasattr(app.root, 'is_playing'):
            # If a song is playing, make sure the bottom bar is visible
            if app.root.is_playing and hasattr(self.ids, 'bottom_bar'):
                self.ids.bottom_bar.opacity = 1
                self.ids.bottom_bar.disabled = False

                # Update the bottom bar UI to reflect the current playing song
                if hasattr(app.root, 'update_ui_play_state'):
                    app.root.update_ui_play_state()

    def search_online(self, query=None):
        """Search for music online using the provided query"""
        logger.debug(f"search_online called with query: {query}")
        if query is not None:
            self.search_query = query

        if not self.search_query.strip():
            logger.debug("Empty search query")
            try:
                # Use Arabic text for the toast message
                toast(get_display_text("الرجاء إدخال كلمة بحث"))
            except Exception as e:
                logger.error(f"Error showing toast: {e}")
            return

        # Show loading indicator
        self.is_searching = True
        self.search_results = []

        # Clear the results list
        if hasattr(self.ids, 'results_list'):
            self.ids.results_list.clear_widgets()

        # Start search in a separate thread
        threading.Thread(target=self._perform_search, daemon=True).start()

    def _perform_search(self):
        """Perform the actual search in a background thread"""
        try:
            # Try multiple search APIs
            results = []

            # First try YouTube search (preferred for playing songs)
            if YOUTUBE_SEARCH_AVAILABLE:
                logger.debug(f"Starting YouTube search for: {self.search_query}")
                youtube_results = self._search_youtube()
                if youtube_results:
                    logger.debug(f"Got {len(youtube_results)} results from YouTube")
                    results.extend(youtube_results)
                else:
                    logger.debug("No results from YouTube")
            else:
                logger.debug("YouTube search not available, skipping")

            # If no YouTube results, try Deezer API (free and no API key required)
            if not results:
                logger.debug(f"Starting Deezer search for: {self.search_query}")
                deezer_results = self._search_deezer()
                if deezer_results:
                    logger.debug(f"Got {len(deezer_results)} results from Deezer")
                    results.extend(deezer_results)
                else:
                    logger.debug("No results from Deezer")

            # Update the UI with results
            logger.debug(f"Scheduling UI update with {len(results)} total results")
            Clock.schedule_once(lambda dt: self._update_results(results), 0)

        except Exception as e:
            logger.error(f"Error searching online: {e}")
            import traceback
            logger.error(traceback.format_exc())
            Clock.schedule_once(lambda dt: self._handle_search_error(str(e)), 0)

    def _search_deezer(self):
        """Search using Deezer API"""
        try:
            # Use the public Deezer API
            encoded_query = quote_plus(self.search_query)
            url = f"https://api.deezer.com/search?q={encoded_query}&limit=20"

            logger.debug(f"Searching with URL: {url}")

            response = requests.get(url, timeout=10)
            logger.debug(f"Response status code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                logger.debug(f"Response data received, total: {data.get('total', 0)}")

                results = []
                if 'data' in data:
                    for item in data['data']:
                        # Get preview URL (30 second clip)
                        preview_url = item.get('preview', '')

                        # Verify the preview URL is valid
                        if preview_url:
                            try:
                                # Check if the URL is accessible
                                head_response = requests.head(preview_url, timeout=5)
                                if head_response.status_code != 200:
                                    logger.warning(f"Preview URL returned status code {head_response.status_code}: {preview_url}")
                                    preview_url = ''
                            except Exception as url_error:
                                logger.warning(f"Error checking preview URL: {url_error}")
                                preview_url = ''

                        # Get full track URL if available
                        track_url = item.get('link', '')

                        # Use the best available URL
                        download_url = preview_url if preview_url else track_url

                        # If no valid URL is available, skip this result
                        if not download_url:
                            logger.warning(f"Skipping result with no valid URL: {item.get('title', 'Unknown')}")
                            continue

                        # Format duration
                        duration_seconds = item.get('duration', 0)
                        duration = self._format_duration(duration_seconds)

                        # Get album cover
                        album = item.get('album', {})
                        thumbnail_url = album.get('cover_medium', '')
                        if not thumbnail_url:
                            thumbnail_url = album.get('cover_small', '')

                        # Create a direct playable URL
                        # For Deezer, the preview URL is already playable
                        playable_url = preview_url

                        result = {
                            'title': item.get('title', 'Unknown Title'),
                            'artist': item.get('artist', {}).get('name', 'Unknown Artist'),
                            'duration': duration,
                            'source': 'Deezer',
                            'download_url': playable_url,  # Use the playable URL
                            'thumbnail_url': thumbnail_url
                        }
                        results.append(result)
                        logger.debug(f"Added result: {result['title']} by {result['artist']} with URL: {playable_url}")

                logger.debug(f"Total results found: {len(results)}")
                return results

            return []

        except Exception as e:
            logger.error(f"Error searching Deezer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    def _search_youtube(self):
        """Search for music on YouTube"""
        if not YOUTUBE_SEARCH_AVAILABLE:
            logger.warning("YouTube search is not available - missing libraries")
            return []

        try:
            logger.debug(f"Starting YouTube search for: {self.search_query}")
            results = []

            # Try using yt-dlp first (more reliable)
            if YTDLP_AVAILABLE:
                try:
                    youtube_results = self._search_youtube_with_ytdlp()
                    if youtube_results:
                        logger.debug(f"Got {len(youtube_results)} results from YouTube using yt-dlp")
                        results.extend(youtube_results)
                        return results
                except Exception as e:
                    logger.warning(f"Error searching YouTube with yt-dlp: {e}. Trying pytube.")

            # If yt-dlp fails or isn't available, try pytube
            if PYTUBE_AVAILABLE:
                try:
                    youtube_results = self._search_youtube_with_pytube()
                    if youtube_results:
                        logger.debug(f"Got {len(youtube_results)} results from YouTube using pytube")
                        results.extend(youtube_results)
                        return results
                except Exception as e:
                    logger.error(f"Error searching YouTube with pytube: {e}")

            return results

        except Exception as e:
            logger.error(f"Error searching YouTube: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    def _search_youtube_with_ytdlp(self):
        """Search YouTube using yt-dlp"""
        results = []

        try:
            # Configure yt-dlp options for search
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'skip_download': True,
                'extract_flat': True,
                'format': 'best',
                'noplaylist': False,  # We want to get playlist results too
                'default_search': 'ytsearch10',  # Search for up to 10 videos
            }

            # Create a YoutubeDL object and search
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Search for videos
                search_query = f"ytsearch10:{self.search_query}"  # Limit to 10 results
                logger.debug(f"Searching YouTube with query: {search_query}")

                # Extract info
                search_results = ydl.extract_info(search_query, download=False)

                if search_results and 'entries' in search_results:
                    entries = search_results['entries']
                    logger.debug(f"Found {len(entries)} YouTube results")

                    for entry in entries:
                        if not entry:
                            continue

                        # Get video details
                        video_id = entry.get('id')
                        if not video_id:
                            continue

                        title = entry.get('title', 'Unknown Title')
                        uploader = entry.get('uploader', 'Unknown Artist')
                        duration_seconds = entry.get('duration')

                        # Format duration
                        if duration_seconds:
                            duration = self._format_duration(duration_seconds)
                        else:
                            duration = "Unknown"

                        # Get thumbnail URL
                        thumbnail_url = entry.get('thumbnail', '')
                        if thumbnail_url is None or thumbnail_url == '':
                            # Use a default thumbnail URL based on video ID
                            # Verificar que el video_id sea válido antes de usarlo
                            if video_id and len(video_id) == 11:
                                thumbnail_url = f"https://img.youtube.com/vi/{video_id}/mqdefault.jpg"
                                print(f"Generated thumbnail URL for video ID {video_id}: {thumbnail_url}")
                            else:
                                print(f"Invalid video ID format: {video_id}, using default thumbnail")
                                thumbnail_url = "images/default_album_cover.png"

                        # Create YouTube URL
                        video_url = f"https://www.youtube.com/watch?v={video_id}"

                        # Create result object
                        result = {
                            'title': title,
                            'artist': uploader,
                            'duration': duration,
                            'source': 'YouTube',
                            'download_url': video_url,
                            'thumbnail_url': thumbnail_url,
                            'video_id': video_id
                        }

                        results.append(result)
                        logger.debug(f"Added YouTube result: {result['title']} by {result['artist']}")

                        # Limit to 10 results
                        if len(results) >= 10:
                            break

            return results

        except Exception as e:
            logger.error(f"Error in _search_youtube_with_ytdlp: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    def _search_youtube_with_pytube(self):
        """Search YouTube using pytube"""
        results = []

        try:
            # Use pytube's Search
            s = Search(self.search_query)
            search_results = s.results

            if not search_results:
                logger.warning("No YouTube results found with pytube")
                return []

            logger.debug(f"Found {len(search_results)} YouTube results with pytube")

            # Process results
            for video in search_results[:10]:  # Limit to 10 results
                try:
                    # Get video details
                    title = video.title
                    uploader = video.author
                    video_id = video.video_id

                    # Get duration
                    duration_seconds = video.length
                    duration = self._format_duration(duration_seconds)

                    # Get thumbnail URL - ensure it's never None
                    # Verificar que el video_id sea válido antes de usarlo
                    if video_id and len(video_id) == 11:
                        thumbnail_url = f"https://img.youtube.com/vi/{video_id}/mqdefault.jpg"
                        print(f"Generated thumbnail URL for video ID {video_id}: {thumbnail_url}")
                    else:
                        print(f"Invalid video ID format: {video_id}, using default thumbnail")
                        thumbnail_url = "images/default_album_cover.png"

                    # Create YouTube URL
                    video_url = f"https://www.youtube.com/watch?v={video_id}"

                    # Create result object
                    result = {
                        'title': title,
                        'artist': uploader,
                        'duration': duration,
                        'source': 'YouTube',
                        'download_url': video_url,
                        'thumbnail_url': thumbnail_url,
                        'video_id': video_id
                    }

                    results.append(result)
                    logger.debug(f"Added YouTube result: {result['title']} by {result['artist']}")

                except Exception as video_error:
                    logger.error(f"Error processing YouTube video: {video_error}")
                    continue

            return results

        except Exception as e:
            logger.error(f"Error in _search_youtube_with_pytube: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    @mainthread
    def _update_results(self, results):
        """Update the UI with search results"""
        logger.debug(f"Updating results UI with {len(results)} results")
        self.is_searching = False
        self.search_results = results

        # Clear the results list
        if hasattr(self.ids, 'results_list'):
            self.ids.results_list.clear_widgets()
        else:
            logger.error("Cannot find results_list in ids")
            return

        # Get app colors
        from kivymd.app import MDApp
        app = MDApp.get_running_app()
        primary_color = app.root.get_primary_color() if app and hasattr(app, 'root') else [0.3, 0.8, 1, 1]
        bg_color = app.root.get_bg_color() if app and hasattr(app, 'root') else [0.9, 0.9, 0.9, 1]

        if not results:
            # Show no results message with improved UI
            try:
                from kivymd.uix.card import MDCard
                from kivymd.uix.boxlayout import MDBoxLayout

                # Create a no results card
                no_results_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height="150dp",
                    md_bg_color=bg_color,
                    radius=[15, 15, 15, 15],
                    elevation=2,
                    padding=[15, 15, 15, 15],
                    margin=[20, 20, 20, 20]
                )

                # Add emoji icon
                emoji_label = MDLabel(
                    text="🔍",  # Search emoji
                    font_size="48sp",
                    halign="center",
                    theme_text_color="Custom",
                    text_color=primary_color,
                    size_hint_y=None,
                    height="60dp"
                )
                no_results_card.add_widget(emoji_label)

                # Add no results message
                no_results_text = get_display_text("لم يتم العثور على نتائج")
                no_results = MDLabel(
                    text=no_results_text,
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body1",
                    font_name=self._get_app_font(),
                    font_size="18sp"
                )
                no_results_card.add_widget(no_results)

                # Add suggestion
                suggestion_text = get_display_text("حاول استخدام كلمات بحث مختلفة")
                suggestion = MDLabel(
                    text=suggestion_text,
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Caption",
                    font_name=self._get_app_font(),
                    font_size="14sp"
                )
                no_results_card.add_widget(suggestion)

                self.ids.results_list.add_widget(no_results_card)
            except Exception as e:
                logger.error(f"Error adding no results card: {e}")
                import traceback
                logger.error(traceback.format_exc())
            return

        # Add a header with results count
        try:
            from kivymd.uix.card import MDCard
            from kivymd.uix.boxlayout import MDBoxLayout

            # Create a header card
            header_card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height="60dp",
                md_bg_color=primary_color,
                radius=[15, 15, 15, 15],
                elevation=2,
                padding=[15, 10, 15, 10],
                margin=[0, 10, 0, 10]
            )

            # Add header content
            header_box = MDBoxLayout(
                orientation="horizontal",
                adaptive_height=True
            )

            # Add search icon
            search_icon = MDLabel(
                text="🔍",  # Search emoji
                font_size="24sp",
                size_hint_x=None,
                width="30dp",
                halign="center",
                theme_text_color="Custom",
                text_color=[1, 1, 1, 1]
            )
            header_box.add_widget(search_icon)

            # Add results count text
            results_text = get_display_text(f"تم العثور على {len(results)} نتيجة")
            results_label = MDLabel(
                text=results_text,
                font_name=self._get_app_font(),
                theme_text_color="Custom",
                text_color=[1, 1, 1, 1],
                font_style="H6",
                font_size="18sp"
            )
            header_box.add_widget(results_label)

            header_card.add_widget(header_box)
            self.ids.results_list.add_widget(header_card)
        except Exception as e:
            logger.error(f"Error adding header: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # Use the real results directly
        all_results = results

        # Update the UI with results
        for index, result in enumerate(all_results):
            try:
                logger.debug(f"Adding result {index+1}: {result.get('title', 'Unknown')}")

                # Ensure thumbnail_url is never None
                thumbnail_url = result.get('thumbnail_url', '')
                if thumbnail_url is None or thumbnail_url == '':
                    # If we have a video_id, use it to create a thumbnail URL
                    if 'video_id' in result and result['video_id']:
                        # Asegurarse de que el video_id sea correcto y no contenga caracteres extraños
                        video_id = result['video_id'].strip()
                        # Verificar que el video_id tenga el formato correcto (generalmente 11 caracteres)
                        if video_id and len(video_id) == 11:
                            thumbnail_url = f"https://img.youtube.com/vi/{video_id}/mqdefault.jpg"
                            print(f"Generated thumbnail URL for video ID {video_id}: {thumbnail_url}")
                        else:
                            print(f"Invalid video ID format: {video_id}, using default thumbnail")
                            thumbnail_url = "images/default_album_cover.png"
                    else:
                        thumbnail_url = "images/default_album_cover.png"  # Use default album cover as fallback

                # Process Arabic text for display
                title = result.get('title', 'Unknown Title')
                artist = result.get('artist', 'Unknown Artist')
                duration = result.get('duration', '0:00')

                # Create the item with all properties and callbacks
                item = SearchResultItem(
                    title=title,
                    artist=artist,
                    duration=duration,
                    source=result.get('source', 'Unknown Source'),
                    download_url=result.get('download_url', ''),
                    thumbnail_url=thumbnail_url,
                    text=get_display_text(title),  # Apply Arabic text handling
                    on_item_click=self.play_search_result,  # Callback for item click
                    on_download=lambda item=None, url=result.get('download_url', ''), title=title: self.download_result(url, title),  # Callback for download button
                    on_stop=self.stop_playback,  # Callback for stop button
                    # Add styling properties that are valid for this widget
                    divider="Full",
                    divider_color=[0.8, 0.8, 0.8, 0.5]
                )

                # Set font properties after creation (these are not valid constructor parameters)
                try:
                    item.font_style = "Subtitle1"
                    # Apply Arabic text handling to the item text
                    if contains_arabic(title):
                        # We'll handle this in the _apply_arabic_text_handling method
                        pass
                except Exception as e:
                    logger.error(f"Error setting font properties: {e}")

                # Add secondary text for artist and duration with Arabic text handling
                try:
                    # Format the secondary text with artist and duration
                    secondary_text = f"{artist} • {duration}"

                    # Apply Arabic text handling if needed
                    if contains_arabic(secondary_text):
                        secondary_text = get_display_text(secondary_text)

                    item.secondary_text = secondary_text
                except Exception as e:
                    logger.error(f"Error setting secondary text: {e}")

                # Add the item to the list
                self.ids.results_list.add_widget(item)

                # Limit to 10 items to avoid performance issues
                if index >= 9:
                    break

            except Exception as e:
                logger.error(f"Error adding result item: {e}")
                import traceback
                logger.error(traceback.format_exc())

    @mainthread
    def _handle_search_error(self, error_message):
        """Handle search errors"""
        self.is_searching = False
        # Apply Arabic text handling to the error message
        error_text = get_display_text(f"خطأ في البحث: {error_message}")
        toast(error_text)

    def download_result(self, url, title):
        """Download a search result"""
        logger.debug(f"download_result called for {title}")

        # Check if we have a valid URL
        if not url:
            toast("No download URL available")
            return

        # Get the main app
        try:
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            logger.debug(f"Got app: {app}")

            if app and hasattr(app, 'root'):
                main_player = app.root
                logger.debug(f"Got main player: {main_player}")

                # Check if the main player has a download manager
                if hasattr(main_player, 'download_manager') and main_player.download_manager:
                    logger.debug(f"Using main player's download manager")
                    download_manager = main_player.download_manager
                else:
                    logger.debug("Main player doesn't have a download manager")
                    toast("Download manager not available")
                    return

                # Add the URL to the download manager
                try:
                    # Show a toast message
                    toast(f"Adding {title} to downloads")

                    # Add the download
                    if hasattr(download_manager, 'add_download'):
                        logger.debug(f"Adding URL to download manager: {url}")
                        download_manager.add_download(url)
                        toast(f"Added {title} to downloads")
                    else:
                        # Try alternative method
                        logger.debug("Using alternative download method")
                        self._download_file(url, title)
                except Exception as e:
                    logger.error(f"Error adding download: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    toast(f"Error adding download: {str(e)}")
            else:
                logger.error("Could not access main player")
                toast("Could not access music player")

        except Exception as e:
            logger.error(f"Error in download_result: {e}")
            import traceback
            logger.error(traceback.format_exc())
            toast(f"Error downloading: {str(e)}")

    def _download_file(self, url, title):
        """Alternative method to download a file"""
        try:
            import os
            import requests
            from pathlib import Path

            # Create downloads directory if it doesn't exist
            downloads_dir = os.path.join(os.path.expanduser("~"), "Downloads", "MusicPlayer")
            os.makedirs(downloads_dir, exist_ok=True)

            # Create a valid filename
            filename = "".join(c for c in title if c.isalnum() or c in " ._-").strip()
            filename = filename.replace(" ", "_") + ".mp3"
            filepath = os.path.join(downloads_dir, filename)

            # Download the file in a separate thread
            def download_thread():
                try:
                    # Download the file
                    response = requests.get(url, stream=True)
                    total_size = int(response.headers.get('content-length', 0))

                    # Save the file
                    with open(filepath, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)

                    # Show success message
                    from kivymd.app import MDApp
                    app = MDApp.get_running_app()
                    if app:
                        app.root.add_toast(f"Downloaded {title} to {downloads_dir}")
                except Exception as e:
                    logger.error(f"Error downloading file: {e}")
                    from kivymd.app import MDApp
                    app = MDApp.get_running_app()
                    if app:
                        app.root.add_toast(f"Error downloading {title}: {str(e)}")

            # Start the download thread
            import threading
            threading.Thread(target=download_thread, daemon=True).start()

            # Show a toast message
            toast(f"Started downloading {title}")

        except Exception as e:
            logger.error(f"Error in _download_file: {e}")
            import traceback
            logger.error(traceback.format_exc())
            toast(f"Error downloading: {str(e)}")

    def stop_playback(self, item):
        """Stop playback when stop button is clicked"""
        logger.debug(f"stop_playback called for {item.title}")

        # Show a toast message
        toast(f"Stopped playing {item.title}")

        # Get the main app using App.get_running_app()
        try:
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            logger.debug(f"Got app: {app}")

            if app and hasattr(app, 'root'):
                main_player = app.root
                logger.debug(f"Got main player: {main_player}")

                # Direct method to stop playback
                if hasattr(main_player, 'stop'):
                    logger.debug("Calling stop method")
                    main_player.stop()
                    return

                # Alternative method
                if hasattr(main_player, 'sound') and main_player.sound:
                    logger.debug("Stopping current sound")
                    main_player.sound.stop()
                    main_player.sound = None

                    # Update player state
                    if hasattr(main_player, 'is_playing'):
                        main_player.is_playing = False

                    # Fix play button if available
                    if hasattr(main_player, 'fix_play_button'):
                        logger.debug("Fixing play button")
                        main_player.fix_play_button()
                else:
                    logger.debug("No sound is currently playing")
            else:
                logger.error("Could not access main player")
                toast("Could not access music player")

        except Exception as e:
            logger.error(f"Error stopping playback: {e}")
            import traceback
            logger.error(traceback.format_exc())
            toast(f"Error stopping playback: {str(e)}")

    def play_search_result(self, item):
        """Play a search result when clicked"""
        logger.debug(f"play_search_result called for {item.title}")

        # Check if we have a download URL
        if not item.download_url:
            toast("No playable URL available")
            return

        # Check if this is a YouTube URL
        is_youtube = False
        if hasattr(item, 'source') and item.source == 'YouTube':
            is_youtube = True
        elif "youtube.com" in item.download_url or "youtu.be" in item.download_url:
            is_youtube = True

        # استخراج معرف الفيديو للتحسين السريع
        video_id = None
        if is_youtube:
            import re
            # استخراج معرف الفيديو من الرابط
            patterns = [
                r'(?:youtube\.com/watch\?v=|youtu\.be/)([a-zA-Z0-9_-]{11})',
                r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
                r'youtube\.com/v/([a-zA-Z0-9_-]{11})'
            ]
            for pattern in patterns:
                match = re.search(pattern, item.download_url)
                if match:
                    video_id = match.group(1)
                    break

        # Show a toast message
        toast(f"Playing {item.title}")

        # Actualizar el estado de reproducción de todos los elementos
        # Primero, desactivar todos los elementos
        if hasattr(self.ids, 'results_list'):
            for child in self.ids.results_list.children:
                if isinstance(child, SearchResultItem):
                    child.is_playing = False

            # Luego, activar solo el elemento seleccionado
            item.is_playing = True

        # Get the main app using App.get_running_app()
        try:
            from kivymd.app import MDApp
            app = MDApp.get_running_app()
            logger.debug(f"Got app: {app}")

            if app and hasattr(app, 'root'):
                main_player = app.root
                logger.debug(f"Got main player: {main_player}")

                # For YouTube URLs, we need special handling
                if is_youtube:
                    logger.debug(f"Playing YouTube URL: {item.download_url}")

                    # عرض مؤشر التحميل قبل التشغيل
                    if hasattr(main_player, 'loading_indicator') and main_player.loading_indicator:
                        loading_message = f"جاري تشغيل: {item.title}"
                        main_player.loading_indicator.show_loading(loading_message, "online_extraction", cancelable=True)

                    # استخدام التشغيل المبسط أولاً
                    if hasattr(main_player, 'play_simple'):
                        logger.debug(f"Using simple playback for: {item.title}")
                        success = main_player.play_simple(item.download_url, item.title, is_online=True)

                        if success:
                            # Asegurarse de que el bottom bar sea visible en la pantalla principal
                            if hasattr(main_player.ids, 'bottom_bar'):
                                main_player.ids.bottom_bar.opacity = 1
                                main_player.ids.bottom_bar.disabled = False
                                logger.debug("Bottom bar made visible in main screen")

                            # También hacer visible la barra inferior en la pantalla de búsqueda
                            if hasattr(self.ids, 'bottom_bar'):
                                self.ids.bottom_bar.opacity = 1
                                self.ids.bottom_bar.disabled = False
                                logger.debug("Bottom bar made visible in search screen")

                            # Actualizar la pantalla "Now Playing" si está abierta
                            if hasattr(main_player, 'update_now_playing_ui'):
                                main_player.update_now_playing_ui()
                                logger.debug("Updated Now Playing UI")

                            return

                    # استخدام التبديل السريع كبديل
                    if video_id and hasattr(main_player, 'fast_online_switch'):
                        logger.debug(f"Using fast online switch for video ID: {video_id}")
                        success = main_player.fast_online_switch(video_id, item.title, item.artist, item.thumbnail_url)

                        if success:
                            # Asegurarse de que el bottom bar sea visible en la pantalla principal
                            if hasattr(main_player.ids, 'bottom_bar'):
                                main_player.ids.bottom_bar.opacity = 1
                                main_player.ids.bottom_bar.disabled = False
                                logger.debug("Bottom bar made visible in main screen")

                            # También hacer visible la barra inferior en la pantalla de búsqueda
                            if hasattr(self.ids, 'bottom_bar'):
                                self.ids.bottom_bar.opacity = 1
                                self.ids.bottom_bar.disabled = False
                                logger.debug("Bottom bar made visible in search screen")

                            # Actualizar la pantalla "Now Playing" si está abierta
                            if hasattr(main_player, 'update_now_playing_ui'):
                                main_player.update_now_playing_ui()
                                logger.debug("Updated Now Playing UI")

                            return

                    # Check if we have a direct play method for YouTube
                    if hasattr(main_player, 'play_youtube'):
                        logger.debug(f"Calling play_youtube with URL: {item.download_url}")
                        success = main_player.play_youtube(item.download_url, item.title, item.artist, item.thumbnail_url)

                        if success:
                            # Asegurarse de que el bottom bar sea visible en la pantalla principal
                            if hasattr(main_player.ids, 'bottom_bar'):
                                main_player.ids.bottom_bar.opacity = 1
                                main_player.ids.bottom_bar.disabled = False
                                logger.debug("Bottom bar made visible in main screen")

                            # También hacer visible la barra inferior en la pantalla de búsqueda
                            if hasattr(self.ids, 'bottom_bar'):
                                self.ids.bottom_bar.opacity = 1
                                self.ids.bottom_bar.disabled = False
                                logger.debug("Bottom bar made visible in search screen")

                            # Actualizar la pantalla "Now Playing" si está abierta
                            if hasattr(main_player, 'update_now_playing_ui'):
                                main_player.update_now_playing_ui()
                                logger.debug("Updated Now Playing UI")

                            return
                        else:
                            logger.warning("play_youtube returned False, trying alternative methods")
                    else:
                        logger.debug("play_youtube method not available, trying alternatives")

                    # If play_youtube failed or is not available, try to extract the audio URL
                    # and use play_url instead (which will download the file first)
                    if hasattr(item, 'video_id') and item.video_id:
                        # Construct a YouTube URL from the video ID
                        youtube_url = f"https://www.youtube.com/watch?v={item.video_id}"
                        logger.debug(f"Using video_id to create URL: {youtube_url}")

                        if hasattr(main_player, 'play_url'):
                            logger.debug(f"Calling play_url with YouTube URL: {youtube_url}")
                            success = main_player.play_url(youtube_url, item.title, item.artist, item.thumbnail_url)

                            if success:
                                # Asegurarse de que el bottom bar sea visible en la pantalla principal
                                if hasattr(main_player.ids, 'bottom_bar'):
                                    main_player.ids.bottom_bar.opacity = 1
                                    main_player.ids.bottom_bar.disabled = False
                                    logger.debug("Bottom bar made visible in main screen")

                                # También hacer visible la barra inferior en la pantalla de búsqueda
                                if hasattr(self.ids, 'bottom_bar'):
                                    self.ids.bottom_bar.opacity = 1
                                    self.ids.bottom_bar.disabled = False
                                    logger.debug("Bottom bar made visible in search screen")

                                # Actualizar la pantalla "Now Playing" si está abierta
                                if hasattr(main_player, 'update_now_playing_ui'):
                                    main_player.update_now_playing_ui()
                                    logger.debug("Updated Now Playing UI")

                                return

                    # If we have yt-dlp or pytube, we can extract the audio URL
                    audio_url = self._get_youtube_audio_url(item.download_url)
                    if audio_url:
                        logger.debug(f"Got YouTube audio URL: {audio_url}")
                        # Use the audio URL instead
                        if hasattr(main_player, 'play_url'):
                            logger.debug(f"Calling play_url with audio URL: {audio_url}")
                            success = main_player.play_url(audio_url, item.title, item.artist, item.thumbnail_url)

                            if success:
                                # Asegurarse de que el bottom bar sea visible en la pantalla principal
                                if hasattr(main_player.ids, 'bottom_bar'):
                                    main_player.ids.bottom_bar.opacity = 1
                                    main_player.ids.bottom_bar.disabled = False
                                    logger.debug("Bottom bar made visible in main screen")

                                # También hacer visible la barra inferior en la pantalla de búsqueda
                                if hasattr(self.ids, 'bottom_bar'):
                                    self.ids.bottom_bar.opacity = 1
                                    self.ids.bottom_bar.disabled = False
                                    logger.debug("Bottom bar made visible in search screen")

                                # Actualizar la pantalla "Now Playing" si está abierta
                                if hasattr(main_player, 'update_now_playing_ui'):
                                    main_player.update_now_playing_ui()
                                    logger.debug("Updated Now Playing UI")

                                return
                    else:
                        # If we couldn't get the audio URL, try the regular URL
                        logger.debug("Could not extract YouTube audio URL, trying regular URL")

                # Direct method to play the URL
                if hasattr(main_player, 'play_url'):
                    logger.debug(f"Calling play_url with URL: {item.download_url}")
                    success = main_player.play_url(item.download_url, item.title, item.artist, item.thumbnail_url)

                    if success:
                        # Asegurarse de que el bottom bar sea visible en la pantalla principal
                        if hasattr(main_player.ids, 'bottom_bar'):
                            main_player.ids.bottom_bar.opacity = 1
                            main_player.ids.bottom_bar.disabled = False
                            logger.debug("Bottom bar made visible in main screen")

                        # También hacer visible la barra inferior en la pantalla de búsqueda
                        if hasattr(self.ids, 'bottom_bar'):
                            self.ids.bottom_bar.opacity = 1
                            self.ids.bottom_bar.disabled = False
                            logger.debug("Bottom bar made visible in search screen")

                        # Actualizar la pantalla "Now Playing" si está abierta
                        if hasattr(main_player, 'update_now_playing_ui'):
                            main_player.update_now_playing_ui()
                            logger.debug("Updated Now Playing UI")

                        return

                # Alternative method using SoundLoader directly
                logger.debug("Using SoundLoader directly")
                from kivy.core.audio import SoundLoader

                # Stop current playback if any
                if hasattr(main_player, 'sound') and main_player.sound:
                    logger.debug("Stopping current sound")
                    main_player.sound.stop()
                    main_player.sound = None

                # For YouTube URLs, we need to download first
                if is_youtube:
                    logger.debug("YouTube URL detected, downloading to temporary file first")
                    try:
                        import tempfile
                        import requests
                        import threading

                        # Create a temporary file
                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                        temp_path = temp_file.name
                        temp_file.close()

                        logger.debug(f"Created temporary file: {temp_path}")

                        # Download the file
                        def download_thread():
                            try:
                                response = requests.get(item.download_url, stream=True)
                                with open(temp_path, 'wb') as f:
                                    for chunk in response.iter_content(chunk_size=32768):
                                        if chunk:
                                            f.write(chunk)

                                logger.debug(f"Download complete, loading from: {temp_path}")

                                # Load and play the file
                                def play_downloaded():
                                    sound = SoundLoader.load(temp_path)
                                    if sound:
                                        logger.debug("Sound loaded successfully, playing")
                                        sound.play()

                                        # Update main player properties
                                        main_player.sound = sound
                                        main_player.is_playing = True

                                        # Update UI
                                        if hasattr(main_player.ids, 'current_track_name'):
                                            main_player.ids.current_track_name.text = item.title

                                        # Start progress timer if available
                                        if hasattr(main_player, 'start_progress_timer'):
                                            main_player.start_progress_timer()

                                        # Fix play button if available
                                        if hasattr(main_player, 'fix_play_button'):
                                            main_player.fix_play_button()

                                        # Make bottom bar visible in main screen
                                        if hasattr(main_player.ids, 'bottom_bar'):
                                            main_player.ids.bottom_bar.opacity = 1
                                            main_player.ids.bottom_bar.disabled = False
                                            logger.debug("Bottom bar made visible in main screen")

                                        # También hacer visible la barra inferior en la pantalla de búsqueda
                                        if hasattr(self.ids, 'bottom_bar'):
                                            self.ids.bottom_bar.opacity = 1
                                            self.ids.bottom_bar.disabled = False
                                            logger.debug("Bottom bar made visible in search screen")

                                        # Update Now Playing screen if open
                                        if hasattr(main_player, 'update_now_playing_ui'):
                                            main_player.update_now_playing_ui()
                                    else:
                                        logger.error(f"Failed to load sound from temporary file: {temp_path}")
                                        toast("Failed to load audio")

                                # Schedule playing on the main thread
                                from kivy.clock import Clock
                                Clock.schedule_once(lambda dt: play_downloaded(), 0)

                            except Exception as e:
                                logger.error(f"Error downloading YouTube audio: {e}")
                                toast(f"Error downloading audio: {str(e)}")

                        # Start the download thread
                        download_thread = threading.Thread(target=download_thread)
                        download_thread.daemon = True
                        download_thread.start()

                        return

                    except Exception as e:
                        logger.error(f"Error setting up YouTube download: {e}")
                        # Fall back to direct loading

                # Load and play the new URL directly (for non-YouTube URLs)
                logger.debug(f"Loading URL directly: {item.download_url}")
                sound = SoundLoader.load(item.download_url)

                if sound:
                    logger.debug("Sound loaded successfully, playing")
                    sound.play()

                    # Update main player properties
                    main_player.sound = sound
                    main_player.is_playing = True

                    # Update UI
                    if hasattr(main_player.ids, 'current_track_name'):
                        logger.debug(f"Updating current_track_name to: {item.title}")
                        main_player.ids.current_track_name.text = item.title

                    # Start progress timer if available
                    if hasattr(main_player, 'start_progress_timer'):
                        logger.debug("Starting progress timer")
                        main_player.start_progress_timer()

                    # Fix play button if available
                    if hasattr(main_player, 'fix_play_button'):
                        logger.debug("Fixing play button")
                        main_player.fix_play_button()

                    # Asegurarse de que el bottom bar sea visible en la pantalla principal
                    if hasattr(main_player.ids, 'bottom_bar'):
                        main_player.ids.bottom_bar.opacity = 1
                        main_player.ids.bottom_bar.disabled = False
                        logger.debug("Bottom bar made visible in main screen")

                    # También hacer visible la barra inferior en la pantalla de búsqueda
                    if hasattr(self.ids, 'bottom_bar'):
                        self.ids.bottom_bar.opacity = 1
                        self.ids.bottom_bar.disabled = False
                        logger.debug("Bottom bar made visible in search screen")

                    # Actualizar la pantalla "Now Playing" si está abierta
                    if hasattr(main_player, 'update_now_playing_ui'):
                        main_player.update_now_playing_ui()
                        logger.debug("Updated Now Playing UI")
                else:
                    logger.error(f"Failed to load sound from URL: {item.download_url}")
                    toast("Failed to load audio")
            else:
                logger.error("Could not access main player")
                toast("Could not access music player")

        except Exception as e:
            logger.error(f"Error playing search result: {e}")
            import traceback
            logger.error(traceback.format_exc())
            toast(f"Error playing song: {str(e)}")

    def _get_youtube_audio_url(self, youtube_url):
        """Extract audio URL from YouTube video URL"""
        if not youtube_url:
            return None

        try:
            # Try using yt-dlp first (more reliable)
            if YTDLP_AVAILABLE:
                try:
                    return self._get_youtube_audio_url_with_ytdlp(youtube_url)
                except Exception as e:
                    logger.warning(f"Error getting YouTube audio URL with yt-dlp: {e}. Trying pytube.")

            # If yt-dlp fails or isn't available, try pytube
            if PYTUBE_AVAILABLE:
                try:
                    return self._get_youtube_audio_url_with_pytube(youtube_url)
                except Exception as e:
                    logger.error(f"Error getting YouTube audio URL with pytube: {e}")

            return None

        except Exception as e:
            logger.error(f"Error in _get_youtube_audio_url: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _get_youtube_audio_url_with_ytdlp(self, youtube_url):
        """Get audio URL from YouTube video using yt-dlp"""
        try:
            # Configure yt-dlp options for extracting audio
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'format': 'bestaudio/best',
                'noplaylist': True,
                'skip_download': True,
                'extract_flat': False,
            }

            # Create a YoutubeDL object and extract info
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(youtube_url, download=False)

                if info:
                    # Get the URL of the best audio format
                    if 'url' in info:
                        return info['url']

                    # If direct URL not found, try formats
                    if 'formats' in info:
                        for format in info['formats']:
                            if format.get('acodec') != 'none' and format.get('vcodec') == 'none':
                                # This is an audio-only format
                                return format.get('url')

                        # If no audio-only format, use the first format with audio
                        for format in info['formats']:
                            if format.get('acodec') != 'none':
                                return format.get('url')

            return None

        except Exception as e:
            logger.error(f"Error in _get_youtube_audio_url_with_ytdlp: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    def _get_youtube_audio_url_with_pytube(self, youtube_url):
        """Get audio URL from YouTube video using pytube"""
        try:
            # Create YouTube object
            yt = YouTube(youtube_url)

            # Get the best audio stream
            audio_stream = yt.streams.filter(only_audio=True).first()

            if audio_stream:
                # Get the direct URL
                return audio_stream.url

            return None

        except Exception as e:
            logger.error(f"Error in _get_youtube_audio_url_with_pytube: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    def _format_duration(self, seconds):
        """Format duration in seconds to MM:SS format"""
        try:
            seconds = int(seconds)
            minutes, seconds = divmod(seconds, 60)
            return f"{minutes}:{seconds:02d}"
        except:
            return "0:00"

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
