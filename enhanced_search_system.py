"""
نظام البحث المحسن - Enhanced Search System
يوفر بحث متقدم وذكي في قائمة الأغاني مع دعم شامل للعربية والإنجليزية
"""

import os
import re
import logging
import threading
import time
from difflib import SequenceMatcher
from collections import defaultdict
from kivy.clock import Clock

logger = logging.getLogger(__name__)

class EnhancedSearchSystem:
    """نظام البحث المحسن والذكي"""
    
    def __init__(self, app):
        self.app = app
        
        # إعدادات البحث
        self.search_settings = {
            'fuzzy_search': True,           # البحث الضبابي
            'instant_search': True,         # البحث الفوري
            'search_in_metadata': True,     # البحث في البيانات الوصفية
            'search_in_filename': True,     # البحث في اسم الملف
            'search_in_path': False,        # البحث في المسار
            'case_sensitive': False,        # حساسية الأحرف
            'min_similarity': 0.6,          # الحد الأدنى للتشابه
            'max_results': 100,             # الحد الأقصى للنتائج
            'search_delay': 0.3,            # تأخير البحث (ثانية)
        }
        
        # ذاكرة التخزين المؤقت للبحث
        self.search_cache = {}
        self.metadata_cache = {}
        self.search_index = {}
        
        # إحصائيات البحث
        self.search_stats = {
            'total_searches': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'average_search_time': 0,
            'most_searched_terms': defaultdict(int)
        }
        
        # متغيرات البحث
        self.current_search_query = ""
        self.search_timer = None
        self.search_thread = None
        self.is_searching = False
        
        # فهرسة الأغاني
        self._build_search_index()
        
        logger.info("🔍 Enhanced Search System initialized")

    def _build_search_index(self):
        """بناء فهرس البحث للأغاني"""
        try:
            if not hasattr(self.app, 'playlist') or not self.app.playlist:
                return
            
            logger.info("🔍 Building search index...")
            start_time = time.time()
            
            for song_path in self.app.playlist:
                try:
                    # استخراج معلومات الأغنية
                    song_info = self._extract_song_info(song_path)
                    
                    # إنشاء كلمات مفتاحية للبحث
                    keywords = self._generate_keywords(song_info)
                    
                    # إضافة إلى الفهرس
                    self.search_index[song_path] = {
                        'info': song_info,
                        'keywords': keywords,
                        'search_text': ' '.join(keywords).lower()
                    }
                    
                except Exception as e:
                    logger.debug(f"Error indexing song {song_path}: {e}")
            
            build_time = time.time() - start_time
            logger.info(f"✅ Search index built in {build_time:.2f}s for {len(self.search_index)} songs")
            
        except Exception as e:
            logger.error(f"Error building search index: {e}")

    def _extract_song_info(self, song_path):
        """استخراج معلومات الأغنية"""
        try:
            # فحص ذاكرة التخزين المؤقت أولاً
            if song_path in self.metadata_cache:
                return self.metadata_cache[song_path]
            
            # معلومات أساسية من اسم الملف
            filename = os.path.basename(song_path)
            name_without_ext = os.path.splitext(filename)[0]
            
            song_info = {
                'filename': filename,
                'title': name_without_ext,
                'artist': 'Unknown',
                'album': 'Unknown',
                'path': song_path,
                'directory': os.path.dirname(song_path)
            }
            
            # محاولة استخراج معلومات من اسم الملف
            # نمط: Artist - Title
            if ' - ' in name_without_ext:
                parts = name_without_ext.split(' - ', 1)
                if len(parts) == 2:
                    song_info['artist'] = parts[0].strip()
                    song_info['title'] = parts[1].strip()
            
            # محاولة استخراج معلومات من المسار
            path_parts = song_path.split(os.sep)
            if len(path_parts) >= 2:
                # المجلد الأخير قد يكون اسم الألبوم
                potential_album = path_parts[-2]
                if potential_album and not potential_album.startswith('.'):
                    song_info['album'] = potential_album
            
            # محاولة استخراج معلومات من البيانات الوصفية
            try:
                if hasattr(self.app, 'extract_metadata'):
                    metadata = self.app.extract_metadata(song_path)
                    if metadata:
                        song_info.update(metadata)
            except Exception as e:
                logger.debug(f"Could not extract metadata for {song_path}: {e}")
            
            # حفظ في ذاكرة التخزين المؤقت
            self.metadata_cache[song_path] = song_info
            
            return song_info
            
        except Exception as e:
            logger.error(f"Error extracting song info: {e}")
            return {
                'filename': os.path.basename(song_path),
                'title': os.path.splitext(os.path.basename(song_path))[0],
                'artist': 'Unknown',
                'album': 'Unknown',
                'path': song_path
            }

    def _generate_keywords(self, song_info):
        """توليد كلمات مفتاحية للبحث"""
        try:
            keywords = []
            
            # إضافة جميع القيم النصية
            for key, value in song_info.items():
                if isinstance(value, str) and value and value != 'Unknown':
                    # تنظيف النص
                    cleaned_value = self._clean_text(value)
                    keywords.append(cleaned_value)
                    
                    # إضافة كلمات منفصلة
                    words = cleaned_value.split()
                    keywords.extend(words)
            
            # إزالة التكرارات والكلمات الفارغة
            keywords = list(set([k for k in keywords if k and len(k) > 1]))
            
            return keywords
            
        except Exception as e:
            logger.error(f"Error generating keywords: {e}")
            return []

    def _clean_text(self, text):
        """تنظيف النص للبحث"""
        try:
            # إزالة الأحرف الخاصة والأرقام غير المرغوبة
            cleaned = re.sub(r'[^\w\s\u0600-\u06FF]', ' ', text)
            
            # إزالة المسافات الزائدة
            cleaned = ' '.join(cleaned.split())
            
            return cleaned.strip()
            
        except Exception as e:
            logger.debug(f"Error cleaning text: {e}")
            return text

    def search_songs(self, query, callback=None):
        """البحث في الأغاني"""
        try:
            if not query or not query.strip():
                # إرجاع جميع الأغاني إذا كان البحث فارغ
                if callback:
                    Clock.schedule_once(lambda dt: callback(self.app.playlist), 0)
                return self.app.playlist
            
            # تنظيف استعلام البحث
            clean_query = self._clean_text(query.strip())
            self.current_search_query = clean_query
            
            # فحص ذاكرة التخزين المؤقت
            cache_key = clean_query.lower()
            if cache_key in self.search_cache:
                self.search_stats['cache_hits'] += 1
                results = self.search_cache[cache_key]
                if callback:
                    Clock.schedule_once(lambda dt: callback(results), 0)
                return results
            
            # إلغاء البحث السابق
            if self.search_timer:
                self.search_timer.cancel()
            
            # بحث فوري أو مؤجل
            if self.search_settings['instant_search']:
                self.search_timer = Clock.schedule_once(
                    lambda dt: self._perform_search(clean_query, callback), 
                    self.search_settings['search_delay']
                )
            else:
                self._perform_search(clean_query, callback)
            
            return []
            
        except Exception as e:
            logger.error(f"Error in search_songs: {e}")
            return []

    def _perform_search(self, query, callback=None):
        """تنفيذ البحث الفعلي"""
        try:
            start_time = time.time()
            self.is_searching = True
            
            # تحديث الإحصائيات
            self.search_stats['total_searches'] += 1
            self.search_stats['most_searched_terms'][query] += 1
            
            # البحث في الفهرس
            results = self._search_in_index(query)
            
            # ترتيب النتائج حسب الصلة
            sorted_results = self._sort_results_by_relevance(query, results)
            
            # تحديد النتائج حسب الحد الأقصى
            final_results = sorted_results[:self.search_settings['max_results']]
            
            # حفظ في ذاكرة التخزين المؤقت
            cache_key = query.lower()
            self.search_cache[cache_key] = final_results
            self.search_stats['cache_misses'] += 1
            
            # تحديث متوسط وقت البحث
            search_time = time.time() - start_time
            if self.search_stats['average_search_time'] == 0:
                self.search_stats['average_search_time'] = search_time
            else:
                self.search_stats['average_search_time'] = (
                    self.search_stats['average_search_time'] * 0.8 + search_time * 0.2
                )
            
            self.is_searching = False
            
            logger.debug(f"🔍 Search completed: '{query}' -> {len(final_results)} results in {search_time:.3f}s")
            
            # استدعاء callback مع النتائج
            if callback:
                Clock.schedule_once(lambda dt: callback(final_results), 0)
            
            return final_results
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            self.is_searching = False
            if callback:
                Clock.schedule_once(lambda dt: callback([]), 0)
            return []

    def _search_in_index(self, query):
        """البحث في الفهرس"""
        try:
            results = []
            query_lower = query.lower()
            query_words = query_lower.split()
            
            for song_path, song_data in self.search_index.items():
                score = 0
                search_text = song_data['search_text']
                
                # البحث المباشر
                if query_lower in search_text:
                    score += 100
                
                # البحث في الكلمات
                for word in query_words:
                    if word in search_text:
                        score += 50
                
                # البحث الضبابي
                if self.search_settings['fuzzy_search']:
                    fuzzy_score = self._fuzzy_match(query_lower, search_text)
                    score += fuzzy_score * 30
                
                # إضافة إلى النتائج إذا كان النتيجة مقبولة
                if score > 0:
                    results.append({
                        'path': song_path,
                        'score': score,
                        'info': song_data['info']
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching in index: {e}")
            return []

    def _fuzzy_match(self, query, text):
        """البحث الضبابي باستخدام التشابه"""
        try:
            # حساب التشابه العام
            similarity = SequenceMatcher(None, query, text).ratio()
            
            if similarity >= self.search_settings['min_similarity']:
                return similarity
            
            # البحث في أجزاء النص
            words = text.split()
            max_similarity = 0
            
            for word in words:
                word_similarity = SequenceMatcher(None, query, word).ratio()
                max_similarity = max(max_similarity, word_similarity)
            
            return max_similarity if max_similarity >= self.search_settings['min_similarity'] else 0
            
        except Exception as e:
            logger.debug(f"Error in fuzzy match: {e}")
            return 0

    def _sort_results_by_relevance(self, query, results):
        """ترتيب النتائج حسب الصلة"""
        try:
            # ترتيب حسب النتيجة (أعلى أولاً)
            sorted_results = sorted(results, key=lambda x: x['score'], reverse=True)
            
            # استخراج مسارات الملفات فقط
            return [result['path'] for result in sorted_results]
            
        except Exception as e:
            logger.error(f"Error sorting results: {e}")
            return [result['path'] for result in results]

    def get_search_suggestions(self, query, max_suggestions=5):
        """الحصول على اقتراحات البحث"""
        try:
            if not query or len(query) < 2:
                return []
            
            suggestions = set()
            query_lower = query.lower()
            
            # البحث في الكلمات المفتاحية
            for song_data in self.search_index.values():
                for keyword in song_data['keywords']:
                    if keyword.lower().startswith(query_lower):
                        suggestions.add(keyword)
                        if len(suggestions) >= max_suggestions:
                            break
                
                if len(suggestions) >= max_suggestions:
                    break
            
            return list(suggestions)[:max_suggestions]
            
        except Exception as e:
            logger.error(f"Error getting search suggestions: {e}")
            return []

    def clear_search_cache(self):
        """مسح ذاكرة التخزين المؤقت للبحث"""
        try:
            self.search_cache.clear()
            logger.info("🧹 Search cache cleared")
        except Exception as e:
            logger.error(f"Error clearing search cache: {e}")

    def rebuild_search_index(self):
        """إعادة بناء فهرس البحث"""
        try:
            self.search_index.clear()
            self.metadata_cache.clear()
            self.search_cache.clear()
            self._build_search_index()
            logger.info("🔄 Search index rebuilt")
        except Exception as e:
            logger.error(f"Error rebuilding search index: {e}")

    def update_search_settings(self, **settings):
        """تحديث إعدادات البحث"""
        try:
            for key, value in settings.items():
                if key in self.search_settings:
                    self.search_settings[key] = value
                    logger.debug(f"Updated search setting {key}: {value}")
            
            # مسح ذاكرة التخزين المؤقت عند تغيير الإعدادات
            self.clear_search_cache()
            
        except Exception as e:
            logger.error(f"Error updating search settings: {e}")

    def get_search_stats(self):
        """الحصول على إحصائيات البحث"""
        try:
            cache_hit_rate = 0
            total_requests = self.search_stats['cache_hits'] + self.search_stats['cache_misses']
            if total_requests > 0:
                cache_hit_rate = (self.search_stats['cache_hits'] / total_requests) * 100
            
            return {
                'stats': self.search_stats.copy(),
                'cache_hit_rate': f"{cache_hit_rate:.1f}%",
                'cache_size': len(self.search_cache),
                'index_size': len(self.search_index),
                'settings': self.search_settings.copy()
            }
            
        except Exception as e:
            logger.error(f"Error getting search stats: {e}")
            return {}

    def export_search_index(self, file_path):
        """تصدير فهرس البحث"""
        try:
            import json
            
            export_data = {
                'index': {path: data['info'] for path, data in self.search_index.items()},
                'stats': self.search_stats,
                'settings': self.search_settings
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📤 Search index exported to {file_path}")
            
        except Exception as e:
            logger.error(f"Error exporting search index: {e}")

    def shutdown(self):
        """إيقاف نظام البحث"""
        try:
            if self.search_timer:
                self.search_timer.cancel()
            
            self.is_searching = False
            logger.info("🛑 Enhanced Search System shutdown")
            
        except Exception as e:
            logger.error(f"Error shutting down search system: {e}")

# دالة مساعدة لتطبيق النظام
def apply_enhanced_search_system(app):
    """تطبيق نظام البحث المحسن على التطبيق"""
    try:
        if not hasattr(app, 'enhanced_search'):
            app.enhanced_search = EnhancedSearchSystem(app)
            logger.info("🔍 Enhanced Search System applied to app")
        return app.enhanced_search
    except Exception as e:
        logger.error(f"Failed to apply enhanced search system: {e}")
        return None
