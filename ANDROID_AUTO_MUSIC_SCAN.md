# البحث التلقائي عن الأغاني في الأندرويد - Android Auto Music Scan

## نظرة عامة / Overview

تم إضافة ميزة البحث التلقائي عن الأغاني الموجودة في جهاز الأندرويد إلى مشغل الموسيقى العربي. هذه الميزة تبحث تلقائياً في جميع مجلدات الجهاز للعثور على ملفات الموسيقى وإضافتها إلى قائمة التشغيل.

The Arabic Music Player now includes automatic music scanning functionality for Android devices. This feature automatically searches through device folders to find music files and adds them to the playlist.

## الميزات الجديدة / New Features

### 🔍 البحث التلقائي الذكي / Smart Auto-Scan

- **البحث الشامل**: يبحث في جميع مجلدات التخزين الأساسي والثانوي
- **Comprehensive Search**: Searches through all primary and secondary storage folders

- **مجلدات متعددة**: يشمل Music, Downloads, Audio, Sounds, Ringtones وغيرها
- **Multiple Folders**: Includes Music, Downloads, Audio, Sounds, Ringtones and more

- **دعم SD Card**: يبحث في بطاقة الذاكرة الخارجية إذا كانت متوفرة
- **SD Card Support**: Searches external memory card if available

### 📱 واجهة مستخدم محسنة / Enhanced UI

- **زر البحث في الشريط العلوي**: أيقونة `folder-music` للأندرويد
- **Search Button in Top Bar**: `folder-music` icon for Android

- **زر البحث للحاسوب**: أيقونة `folder-search` للحاسوب المكتبي
- **Desktop Search Button**: `folder-search` icon for desktop computers

- **رسائل تقدم واضحة**: إشعارات للمستخدم أثناء البحث
- **Clear Progress Messages**: User notifications during scanning

### 🔐 إدارة الأذونات / Permission Management

- **فحص الأذونات التلقائي**: يتحقق من أذونات القراءة والكتابة
- **Automatic Permission Check**: Verifies read and write permissions

- **طلب الأذونات**: يطلب الأذونات المفقودة تلقائياً
- **Permission Request**: Automatically requests missing permissions

- **معالجة الأخطاء**: رسائل واضحة عند عدم توفر الأذونات
- **Error Handling**: Clear messages when permissions are unavailable

## كيفية الاستخدام / How to Use

### البحث اليدوي / Manual Scan

1. **افتح التطبيق** / **Open the app**
2. **انقر على أيقونة البحث** في الشريط العلوي / **Click the search icon** in the top bar
3. **انتظر انتهاء البحث** / **Wait for scan completion**
4. **ستظهر الأغاني الجديدة** في قائمة التشغيل / **New songs will appear** in the playlist

### البحث التلقائي / Automatic Scan

- **عند بدء التطبيق**: يتم البحث تلقائياً إذا كانت قائمة التشغيل فارغة
- **On App Start**: Automatically scans if playlist is empty

- **للأندرويد**: البحث بعد 3 ثوانٍ من بدء التطبيق
- **For Android**: Scans 3 seconds after app start

- **للحاسوب**: البحث بعد ثانيتين من بدء التطبيق
- **For Desktop**: Scans 2 seconds after app start

## المجلدات المدعومة / Supported Directories

### الأندرويد / Android

#### التخزين الأساسي / Primary Storage
- `/storage/emulated/0/Music`
- `/storage/emulated/0/Download`
- `/storage/emulated/0/Downloads`
- `/storage/emulated/0/Audio`
- `/storage/emulated/0/Sounds`
- `/storage/emulated/0/Ringtones`
- `/storage/emulated/0/Notifications`
- `/storage/emulated/0/Alarms`
- `/storage/emulated/0/` (الجذر / Root)

#### التخزين الثانوي / Secondary Storage (SD Card)
- `[SD_PATH]/Music`
- `[SD_PATH]/Download`
- `[SD_PATH]/Downloads`
- `[SD_PATH]/Audio`
- `[SD_PATH]/` (الجذر / Root)

#### مسارات احتياطية / Fallback Paths
- `/sdcard/Music`
- `/sdcard/Download`
- `/sdcard/Downloads`
- `/sdcard/`

### الحاسوب المكتبي / Desktop

- `~/Music` (مجلد الموسيقى / Music folder)
- `~/Downloads` (مجلد التحميلات / Downloads folder)
- `~/Desktop` (سطح المكتب / Desktop)
- `./music` (مجلد التطبيق / App folder)
- `./downloads` (مجلد التحميلات المحلي / Local downloads)
- `.` (المجلد الحالي / Current directory)

## تنسيقات الصوت المدعومة / Supported Audio Formats

- **MP3** - الأكثر شيوعاً / Most common
- **WAV** - جودة عالية / High quality
- **OGG** - مفتوح المصدر / Open source
- **M4A** - Apple format
- **FLAC** - بدون فقدان / Lossless
- **AAC** - ضغط متقدم / Advanced compression
- **WMA** - Windows Media
- **AIFF** - Apple format

## الأذونات المطلوبة / Required Permissions

### الأندرويد / Android

```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
```

### الحاسوب المكتبي / Desktop
- لا توجد أذونات خاصة مطلوبة / No special permissions required
- يحتاج فقط إلى حقوق القراءة للمجلدات / Only needs read access to folders

## الميزات التقنية / Technical Features

### 🚀 الأداء / Performance

- **البحث المتوازي**: يبحث في عدة مجلدات في نفس الوقت
- **Parallel Search**: Searches multiple folders simultaneously

- **تجنب التكرار**: لا يضيف نفس الملف مرتين
- **Duplicate Avoidance**: Doesn't add the same file twice

- **ترتيب أبجدي**: ترتيب الأغاني حسب الاسم
- **Alphabetical Sorting**: Sorts songs by name

### 🛡️ الأمان / Security

- **فحص الملفات**: يتحقق من وجود الملف وإمكانية قراءته
- **File Validation**: Checks file existence and readability

- **فحص الحجم**: يتجنب الملفات الفارغة
- **Size Check**: Avoids empty files

- **معالجة الأخطاء**: يتعامل مع الأخطاء بأمان
- **Error Handling**: Safely handles errors

### 📊 التسجيل / Logging

- **سجلات مفصلة**: تسجيل جميع عمليات البحث
- **Detailed Logs**: Records all scan operations

- **إحصائيات**: عدد الملفات المكتشفة والمضافة
- **Statistics**: Number of files found and added

- **تشخيص الأخطاء**: رسائل واضحة للمطورين
- **Error Diagnosis**: Clear messages for developers

## استكشاف الأخطاء / Troubleshooting

### المشكلة: لا يتم العثور على أغاني / Problem: No songs found

**الحلول / Solutions:**

1. **تحقق من الأذونات** / **Check permissions**
   - تأكد من منح أذونات التخزين / Ensure storage permissions are granted
   - أعد تشغيل التطبيق بعد منح الأذونات / Restart app after granting permissions

2. **تحقق من مواقع الملفات** / **Check file locations**
   - تأكد من وجود ملفات موسيقى في المجلدات المدعومة / Ensure music files exist in supported folders
   - انقل الملفات إلى مجلد Music إذا لزم الأمر / Move files to Music folder if needed

3. **تحقق من تنسيقات الملفات** / **Check file formats**
   - تأكد من أن الملفات بتنسيقات مدعومة / Ensure files are in supported formats
   - حول الملفات إلى MP3 إذا لزم الأمر / Convert files to MP3 if needed

### المشكلة: البحث بطيء / Problem: Slow scanning

**الحلول / Solutions:**

1. **قلل عدد الملفات** / **Reduce file count**
   - نظم الملفات في مجلدات فرعية / Organize files in subfolders
   - احذف الملفات غير المرغوب فيها / Delete unwanted files

2. **أعد تشغيل الجهاز** / **Restart device**
   - أعد تشغيل الجهاز لتحسين الأداء / Restart device for better performance
   - أغلق التطبيقات الأخرى / Close other apps

### المشكلة: رسائل خطأ في الأذونات / Problem: Permission error messages

**الحلول / Solutions:**

1. **إعدادات التطبيق** / **App Settings**
   - اذهب إلى إعدادات الجهاز > التطبيقات / Go to Device Settings > Apps
   - اختر مشغل الموسيقى > الأذونات / Select Music Player > Permissions
   - فعّل جميع أذونات التخزين / Enable all storage permissions

2. **إعادة تثبيت** / **Reinstall**
   - احذف التطبيق وأعد تثبيته / Uninstall and reinstall the app
   - امنح الأذونات عند أول تشغيل / Grant permissions on first run

## التحديثات المستقبلية / Future Updates

- **بحث أسرع**: تحسينات في خوارزمية البحث / **Faster Search**: Algorithm improvements
- **مرشحات متقدمة**: تصفية حسب النوع والحجم / **Advanced Filters**: Filter by type and size
- **بحث مجدول**: بحث دوري تلقائي / **Scheduled Scan**: Automatic periodic scanning
- **إعدادات مخصصة**: اختيار مجلدات البحث / **Custom Settings**: Choose scan folders

---

**ملاحظة**: هذه الميزة تعمل بشكل أفضل على الأندرويد مع الأذونات المناسبة.

**Note**: This feature works best on Android with proper permissions granted.
