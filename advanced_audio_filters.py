"""
نظام فلاتر الصوت المتقدم - Advanced Audio Filters System
يوفر فلاتر صوتية متقدمة وتحسين جودة الصوت للحصول على أفضل تجربة استماع
"""

import os
import subprocess
import tempfile
import logging
import numpy as np
import threading
from queue import Queue
from kivy.utils import platform
from kivy.clock import Clock

logger = logging.getLogger(__name__)

class AdvancedAudioFilters:
    """نظام فلاتر الصوت المتقدم"""
    
    def __init__(self, ffmpeg_path=None):
        self.ffmpeg_path = ffmpeg_path or self._find_ffmpeg()
        
        # إعدادات الفلاتر المتقدمة
        self.filters = {
            # فلاتر الإيكولايزر المتقدم
            'equalizer': {
                'sub_bass': 0,      # 20-60 Hz
                'bass': 0,          # 60-250 Hz  
                'low_mid': 0,       # 250-500 Hz
                'mid': 0,           # 500-2000 Hz
                'high_mid': 0,      # 2000-4000 Hz
                'presence': 0,      # 4000-6000 Hz
                'brilliance': 0,    # 6000-20000 Hz
            },
            
            # فلاتر تحسين الصوت
            'enhancement': {
                'loudness_normalization': True,
                'dynamic_range_compression': 0.3,
                'stereo_enhancement': 0.2,
                'bass_boost': 0,
                'vocal_enhancement': False,
                'noise_reduction': 0.1,
                'reverb': 0,
                'echo': 0,
            },
            
            # فلاتر الجودة
            'quality': {
                'upsampling': True,
                'bit_depth_enhancement': True,
                'anti_aliasing': True,
                'harmonic_enhancement': 0.1,
                'spatial_enhancement': 0.15,
            },
            
            # فلاتر التخصص
            'specialty': {
                'tube_warmth': 0,
                'analog_saturation': 0,
                'exciter': 0,
                'maximizer': 0.2,
                'psychoacoustic_enhancement': True,
            }
        }
        
        # إعدادات الجودة
        self.quality_settings = {
            'sample_rate': 48000,
            'bit_rate': '320k',
            'bit_depth': 24,
            'channels': 2,
            'codec': 'libmp3lame',
            'quality': 'high'
        }
        
        # قائمة انتظار المعالجة
        self.processing_queue = Queue()
        self.processing_thread = None
        self.is_processing = False
        
        # ذاكرة التخزين المؤقت للملفات المحسنة
        self.enhanced_cache = {}
        self.cache_dir = self._create_cache_dir()
        
        self._start_processing_thread()
        logger.info("🎛️ Advanced Audio Filters System initialized")

    def _find_ffmpeg(self):
        """البحث عن FFmpeg في النظام"""
        try:
            if platform == 'android':
                # مسارات FFmpeg المحتملة في الأندرويد
                android_paths = [
                    '/data/data/org.kivy.android/files/ffmpeg',
                    '/system/bin/ffmpeg',
                    '/system/xbin/ffmpeg'
                ]
                for path in android_paths:
                    if os.path.exists(path):
                        return path
                return 'ffmpeg'  # افتراضي
            else:
                # للحاسوب
                return 'ffmpeg'
        except Exception as e:
            logger.warning(f"Error finding FFmpeg: {e}")
            return 'ffmpeg'

    def _create_cache_dir(self):
        """إنشاء مجلد ذاكرة التخزين المؤقت"""
        try:
            cache_dir = os.path.join(tempfile.gettempdir(), 'audio_filters_cache')
            os.makedirs(cache_dir, exist_ok=True)
            return cache_dir
        except Exception as e:
            logger.error(f"Error creating cache directory: {e}")
            return tempfile.gettempdir()

    def set_equalizer_advanced(self, **frequencies):
        """تعيين إعدادات الإيكولايزر المتقدم"""
        try:
            for freq, value in frequencies.items():
                if freq in self.filters['equalizer']:
                    # تحديد القيم بين -20 و +20 ديسيبل
                    self.filters['equalizer'][freq] = max(-20, min(20, value))
                    
            logger.info(f"🎛️ Advanced equalizer updated: {self.filters['equalizer']}")
            return self.filters['equalizer']
            
        except Exception as e:
            logger.error(f"Error setting advanced equalizer: {e}")
            return None

    def set_enhancement_filters(self, **enhancements):
        """تعيين فلاتر التحسين"""
        try:
            for filter_name, value in enhancements.items():
                if filter_name in self.filters['enhancement']:
                    self.filters['enhancement'][filter_name] = value
                    
            logger.info(f"🔊 Enhancement filters updated: {self.filters['enhancement']}")
            return self.filters['enhancement']
            
        except Exception as e:
            logger.error(f"Error setting enhancement filters: {e}")
            return None

    def set_quality_mode(self, mode='high'):
        """تعيين وضع الجودة"""
        try:
            quality_modes = {
                'ultra': {
                    'sample_rate': 96000,
                    'bit_rate': '320k',
                    'bit_depth': 32,
                    'codec': 'flac'
                },
                'high': {
                    'sample_rate': 48000,
                    'bit_rate': '320k',
                    'bit_depth': 24,
                    'codec': 'libmp3lame'
                },
                'medium': {
                    'sample_rate': 44100,
                    'bit_rate': '256k',
                    'bit_depth': 16,
                    'codec': 'libmp3lame'
                },
                'low': {
                    'sample_rate': 44100,
                    'bit_rate': '192k',
                    'bit_depth': 16,
                    'codec': 'libmp3lame'
                }
            }
            
            if mode in quality_modes:
                self.quality_settings.update(quality_modes[mode])
                self.quality_settings['quality'] = mode
                logger.info(f"🎵 Quality mode set to: {mode}")
                
        except Exception as e:
            logger.error(f"Error setting quality mode: {e}")

    def apply_preset(self, preset_name):
        """تطبيق إعداد مسبق للفلاتر"""
        try:
            presets = {
                'rock': {
                    'equalizer': {'bass': 6, 'low_mid': 3, 'mid': -2, 'high_mid': 4, 'presence': 5, 'brilliance': 3},
                    'enhancement': {'bass_boost': 0.3, 'dynamic_range_compression': 0.4}
                },
                'pop': {
                    'equalizer': {'bass': 2, 'low_mid': 1, 'mid': 0, 'high_mid': 2, 'presence': 3, 'brilliance': 2},
                    'enhancement': {'vocal_enhancement': True, 'stereo_enhancement': 0.3}
                },
                'classical': {
                    'equalizer': {'sub_bass': -1, 'bass': 0, 'low_mid': 0, 'mid': 0, 'high_mid': 1, 'presence': 2, 'brilliance': 3},
                    'enhancement': {'dynamic_range_compression': 0.1, 'spatial_enhancement': 0.3}
                },
                'jazz': {
                    'equalizer': {'bass': 3, 'low_mid': 2, 'mid': 1, 'high_mid': 1, 'presence': 2, 'brilliance': 1},
                    'enhancement': {'tube_warmth': 0.2, 'analog_saturation': 0.1}
                },
                'electronic': {
                    'equalizer': {'sub_bass': 8, 'bass': 5, 'low_mid': 0, 'mid': -1, 'high_mid': 3, 'presence': 4, 'brilliance': 6},
                    'enhancement': {'bass_boost': 0.4, 'exciter': 0.3}
                },
                'vocal': {
                    'equalizer': {'bass': -2, 'low_mid': -1, 'mid': 3, 'high_mid': 4, 'presence': 5, 'brilliance': 2},
                    'enhancement': {'vocal_enhancement': True, 'noise_reduction': 0.2}
                },
                'bass_boost': {
                    'equalizer': {'sub_bass': 12, 'bass': 8, 'low_mid': 3, 'mid': 0, 'high_mid': 0, 'presence': 0, 'brilliance': 0},
                    'enhancement': {'bass_boost': 0.6, 'maximizer': 0.3}
                },
                'audiophile': {
                    'equalizer': {'sub_bass': 1, 'bass': 1, 'low_mid': 0, 'mid': 0, 'high_mid': 1, 'presence': 2, 'brilliance': 2},
                    'enhancement': {'psychoacoustic_enhancement': True, 'spatial_enhancement': 0.25, 'harmonic_enhancement': 0.15}
                }
            }
            
            if preset_name in presets:
                preset = presets[preset_name]
                
                # تطبيق إعدادات الإيكولايزر
                if 'equalizer' in preset:
                    self.set_equalizer_advanced(**preset['equalizer'])
                    
                # تطبيق فلاتر التحسين
                if 'enhancement' in preset:
                    self.set_enhancement_filters(**preset['enhancement'])
                    
                logger.info(f"🎼 Applied preset: {preset_name}")
                return True
            else:
                logger.warning(f"Unknown preset: {preset_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error applying preset: {e}")
            return False

    def _build_filter_chain(self):
        """بناء سلسلة الفلاتر لـ FFmpeg"""
        try:
            filters = []
            
            # فلاتر الإيكولايزر المتقدم
            eq = self.filters['equalizer']
            if any(v != 0 for v in eq.values()):
                # Sub-bass (20-60 Hz)
                if eq['sub_bass'] != 0:
                    filters.append(f"equalizer=f=40:width_type=o:width=1.5:g={eq['sub_bass']}")
                
                # Bass (60-250 Hz)
                if eq['bass'] != 0:
                    filters.append(f"equalizer=f=155:width_type=o:width=2:g={eq['bass']}")
                
                # Low-mid (250-500 Hz)
                if eq['low_mid'] != 0:
                    filters.append(f"equalizer=f=375:width_type=o:width=1.5:g={eq['low_mid']}")
                
                # Mid (500-2000 Hz)
                if eq['mid'] != 0:
                    filters.append(f"equalizer=f=1250:width_type=o:width=2:g={eq['mid']}")
                
                # High-mid (2000-4000 Hz)
                if eq['high_mid'] != 0:
                    filters.append(f"equalizer=f=3000:width_type=o:width=1.5:g={eq['high_mid']}")
                
                # Presence (4000-6000 Hz)
                if eq['presence'] != 0:
                    filters.append(f"equalizer=f=5000:width_type=o:width=1.5:g={eq['presence']}")
                
                # Brilliance (6000-20000 Hz)
                if eq['brilliance'] != 0:
                    filters.append(f"equalizer=f=13000:width_type=o:width=2:g={eq['brilliance']}")

            # فلاتر التحسين
            enh = self.filters['enhancement']
            
            # تطبيع الصوت
            if enh['loudness_normalization']:
                filters.append("loudnorm=I=-16:TP=-1.5:LRA=11")
            
            # ضغط النطاق الديناميكي
            if enh['dynamic_range_compression'] > 0:
                ratio = 1 + (enh['dynamic_range_compression'] * 9)  # 1:1 to 10:1
                filters.append(f"acompressor=threshold=-18dB:ratio={ratio}:attack=5:release=50")
            
            # تحسين الستيريو
            if enh['stereo_enhancement'] > 0:
                filters.append(f"extrastereo=m={enh['stereo_enhancement']}")
            
            # تعزيز الباص
            if enh['bass_boost'] > 0:
                boost_db = enh['bass_boost'] * 12  # 0-12 dB
                filters.append(f"equalizer=f=80:width_type=o:width=2:g={boost_db}")
            
            # تقليل الضوضاء
            if enh['noise_reduction'] > 0:
                filters.append(f"afftdn=nr={enh['noise_reduction'] * 20}")
            
            # الريفيرب
            if enh['reverb'] > 0:
                filters.append(f"aecho=0.8:0.9:{int(enh['reverb'] * 1000)}:0.3")
            
            # فلاتر الجودة
            qual = self.filters['quality']
            
            # تحسين التردد العالي
            if qual['upsampling'] and self.quality_settings['sample_rate'] > 44100:
                filters.append(f"aresample={self.quality_settings['sample_rate']}")
            
            # مكافحة التشويه
            if qual['anti_aliasing']:
                filters.append("aresample=resampler=soxr")
            
            # تحسين التوافقيات
            if qual['harmonic_enhancement'] > 0:
                filters.append(f"aexciter=amount={qual['harmonic_enhancement']}:blend={qual['harmonic_enhancement']}")
            
            # فلاتر التخصص
            spec = self.filters['specialty']
            
            # دفء الأنبوب
            if spec['tube_warmth'] > 0:
                filters.append(f"asoftclip=type=tanh:param={spec['tube_warmth']}")
            
            # التشبع التناظري
            if spec['analog_saturation'] > 0:
                filters.append(f"asoftclip=type=atan:param={spec['analog_saturation']}")
            
            # المثير
            if spec['exciter'] > 0:
                filters.append(f"aexciter=amount={spec['exciter']}:blend=0.5")
            
            # المعظم
            if spec['maximizer'] > 0:
                filters.append(f"alimiter=level_in=1:level_out={1 - spec['maximizer']}:limit=0.95")
            
            return ','.join(filters) if filters else None
            
        except Exception as e:
            logger.error(f"Error building filter chain: {e}")
            return None

    def enhance_audio_advanced(self, input_path, callback=None):
        """تحسين الصوت بالفلاتر المتقدمة"""
        try:
            # فحص ذاكرة التخزين المؤقت
            cache_key = self._generate_cache_key(input_path)
            cached_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")
            
            if os.path.exists(cached_path):
                logger.info(f"🎵 Using cached enhanced audio: {os.path.basename(input_path)}")
                if callback:
                    Clock.schedule_once(lambda dt: callback(cached_path), 0)
                return cached_path
            
            # إضافة إلى قائمة المعالجة
            self.processing_queue.put((input_path, cached_path, callback))
            logger.info(f"🎛️ Queued for advanced enhancement: {os.path.basename(input_path)}")
            
            return input_path  # إرجاع المسار الأصلي مؤقتاً
            
        except Exception as e:
            logger.error(f"Error in advanced audio enhancement: {e}")
            return input_path

    def _generate_cache_key(self, input_path):
        """توليد مفتاح ذاكرة التخزين المؤقت"""
        try:
            import hashlib
            
            # إنشاء hash من المسار وإعدادات الفلاتر
            content = f"{input_path}_{str(self.filters)}_{str(self.quality_settings)}"
            return hashlib.md5(content.encode()).hexdigest()
            
        except Exception as e:
            logger.error(f"Error generating cache key: {e}")
            return str(hash(input_path))

    def _start_processing_thread(self):
        """بدء خيط المعالجة"""
        try:
            if not self.processing_thread or not self.processing_thread.is_alive():
                self.is_processing = True
                self.processing_thread = threading.Thread(target=self._process_queue, daemon=True)
                self.processing_thread.start()
                logger.info("🔄 Audio processing thread started")
                
        except Exception as e:
            logger.error(f"Error starting processing thread: {e}")

    def _process_queue(self):
        """معالجة قائمة انتظار التحسين"""
        while self.is_processing:
            try:
                if not self.processing_queue.empty():
                    input_path, output_path, callback = self.processing_queue.get(timeout=1)
                    
                    # معالجة الملف
                    success = self._process_audio_file(input_path, output_path)
                    
                    if success and callback:
                        Clock.schedule_once(lambda dt: callback(output_path), 0)
                        
                else:
                    import time
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.debug(f"Processing queue error: {e}")

    def _process_audio_file(self, input_path, output_path):
        """معالجة ملف صوتي واحد"""
        try:
            if not self.ffmpeg_path:
                logger.warning("FFmpeg not available")
                return False
            
            # بناء سلسلة الفلاتر
            filter_chain = self._build_filter_chain()
            
            # بناء أمر FFmpeg
            cmd = [
                self.ffmpeg_path,
                '-y',  # الكتابة فوق الملفات الموجودة
                '-i', input_path,
                '-ar', str(self.quality_settings['sample_rate']),
                '-ab', self.quality_settings['bit_rate'],
                '-ac', str(self.quality_settings['channels']),
                '-c:a', self.quality_settings['codec']
            ]
            
            # إضافة الفلاتر إذا كانت متاحة
            if filter_chain:
                cmd.extend(['-af', filter_chain])
            
            cmd.append(output_path)
            
            # تنفيذ الأمر
            creation_flags = 0
            if platform == 'win':
                creation_flags = subprocess.CREATE_NO_WINDOW
            
            logger.debug(f"🎛️ Processing with filters: {os.path.basename(input_path)}")
            
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=creation_flags,
                timeout=60  # مهلة زمنية لتجنب التعليق
            )
            
            if result.returncode == 0:
                logger.info(f"✅ Advanced enhancement completed: {os.path.basename(input_path)}")
                return True
            else:
                logger.error(f"❌ FFmpeg error: {result.stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing audio file: {e}")
            return False

    def get_available_presets(self):
        """الحصول على قائمة الإعدادات المسبقة المتاحة"""
        return [
            'rock', 'pop', 'classical', 'jazz', 'electronic', 
            'vocal', 'bass_boost', 'audiophile'
        ]

    def get_filter_status(self):
        """الحصول على حالة الفلاتر الحالية"""
        return {
            'equalizer': self.filters['equalizer'].copy(),
            'enhancement': self.filters['enhancement'].copy(),
            'quality': self.filters['quality'].copy(),
            'specialty': self.filters['specialty'].copy(),
            'quality_settings': self.quality_settings.copy()
        }

    def clear_cache(self):
        """مسح ذاكرة التخزين المؤقت"""
        try:
            import shutil
            if os.path.exists(self.cache_dir):
                shutil.rmtree(self.cache_dir)
                self.cache_dir = self._create_cache_dir()
            self.enhanced_cache.clear()
            logger.info("🧹 Audio filters cache cleared")
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")

    def shutdown(self):
        """إيقاف نظام الفلاتر"""
        try:
            self.is_processing = False
            if self.processing_thread:
                self.processing_thread.join(timeout=2)
            logger.info("🛑 Advanced Audio Filters shutdown")
            
        except Exception as e:
            logger.error(f"Error shutting down audio filters: {e}")

# دالة مساعدة لتطبيق النظام
def apply_advanced_audio_filters(app, ffmpeg_path=None):
    """تطبيق نظام الفلاتر المتقدم على التطبيق"""
    try:
        if not hasattr(app, 'advanced_audio_filters'):
            app.advanced_audio_filters = AdvancedAudioFilters(ffmpeg_path)
            logger.info("🎛️ Advanced Audio Filters applied to app")
        return app.advanced_audio_filters
    except Exception as e:
        logger.error(f"Failed to apply advanced audio filters: {e}")
        return None
