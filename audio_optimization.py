"""
Audio Optimization Module for Music Player
Provides advanced audio loading, caching, and playback optimization.
"""

import os
import time
import threading
import logging
from collections import OrderedDict, deque
from kivy.clock import Clock
from kivy.core.audio import SoundLoader

logger = logging.getLogger(__name__)

class AudioOptimizer:
    """Advanced audio optimization and caching system"""
    
    def __init__(self, max_cache_size=5, preload_count=2):
        self.max_cache_size = max_cache_size
        self.preload_count = preload_count
        
        # Audio caches
        self.audio_cache = OrderedDict()  # Loaded audio files
        self.metadata_cache = OrderedDict()  # Audio metadata
        self.preload_queue = deque()  # Files to preload
        
        # Background loading
        self.loading_thread = None
        self.should_load = threading.Event()
        self.cache_lock = threading.Lock()
        
        # Performance tracking
        self.load_times = deque(maxlen=20)
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Audio settings
        self.audio_buffer_size = 4096
        self.preferred_format = 'mp3'
        self.enable_preloading = True
        
        self._start_background_loader()
    
    def _start_background_loader(self):
        """Start background audio loading thread"""
        try:
            self.loading_thread = threading.Thread(
                target=self._background_loading_loop,
                daemon=True
            )
            self.loading_thread.start()
            logger.info("Audio background loader started")
        except Exception as e:
            logger.error(f"Error starting audio loader: {e}")
    
    def _background_loading_loop(self):
        """Background loading loop for audio files"""
        while True:
            try:
                if self.should_load.wait(timeout=10):
                    self.should_load.clear()
                    self._process_preload_queue()
                else:
                    # Periodic cleanup
                    self._cleanup_old_cache()
                    
            except Exception as e:
                logger.error(f"Error in background loading: {e}")
                time.sleep(1)
    
    def _process_preload_queue(self):
        """Process the preload queue"""
        try:
            with self.cache_lock:
                queue_copy = list(self.preload_queue)
                self.preload_queue.clear()
            
            for audio_path in queue_copy:
                try:
                    if not self._is_cached(audio_path):
                        self._preload_audio(audio_path)
                except Exception as e:
                    logger.error(f"Error preloading {audio_path}: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing preload queue: {e}")
    
    def load_audio_optimized(self, audio_path, preload_next=None):
        """Load audio with optimization and caching"""
        try:
            start_time = time.time()
            
            # Check cache first
            with self.cache_lock:
                if audio_path in self.audio_cache:
                    self.audio_cache.move_to_end(audio_path)  # LRU
                    self.cache_hits += 1
                    load_time = time.time() - start_time
                    self.load_times.append(load_time)
                    logger.debug(f"Audio cache hit: {os.path.basename(audio_path)} ({load_time:.3f}s)")
                    return self.audio_cache[audio_path]
            
            # Load audio file
            self.cache_misses += 1
            audio = self._load_audio_file(audio_path)
            
            if audio:
                # Cache the audio
                self._cache_audio(audio_path, audio)
                
                # Queue preloading of next files
                if preload_next and self.enable_preloading:
                    self._queue_preload(preload_next)
                
                load_time = time.time() - start_time
                self.load_times.append(load_time)
                logger.debug(f"Audio loaded: {os.path.basename(audio_path)} ({load_time:.3f}s)")
            
            return audio
            
        except Exception as e:
            logger.error(f"Error loading optimized audio {audio_path}: {e}")
            return None
    
    def _load_audio_file(self, audio_path):
        """Load a single audio file with optimization"""
        try:
            if not os.path.exists(audio_path):
                logger.warning(f"Audio file not found: {audio_path}")
                return None
            
            # Check file size
            file_size = os.path.getsize(audio_path)
            if file_size > 100 * 1024 * 1024:  # 100MB limit
                logger.warning(f"Audio file too large: {audio_path} ({file_size / 1024 / 1024:.1f}MB)")
                return None
            
            # Load with SoundLoader
            audio = SoundLoader.load(audio_path)
            
            if audio:
                # Apply audio optimizations
                self._optimize_audio_settings(audio)
                
                # Cache metadata
                self._cache_metadata(audio_path, {
                    'duration': getattr(audio, 'length', 0),
                    'file_size': file_size,
                    'load_time': time.time()
                })
            
            return audio
            
        except Exception as e:
            logger.error(f"Error loading audio file {audio_path}: {e}")
            return None
    
    def _optimize_audio_settings(self, audio):
        """Apply optimization settings to audio object"""
        try:
            # Set buffer size if supported
            if hasattr(audio, 'set_buffer_size'):
                audio.set_buffer_size(self.audio_buffer_size)
            
            # Set other audio properties
            if hasattr(audio, 'pitch'):
                audio.pitch = 1.0  # Normal pitch
            
        except Exception as e:
            logger.error(f"Error optimizing audio settings: {e}")
    
    def _cache_audio(self, audio_path, audio):
        """Cache audio with memory management"""
        try:
            with self.cache_lock:
                # Remove oldest if cache is full
                while len(self.audio_cache) >= self.max_cache_size:
                    oldest_path = next(iter(self.audio_cache))
                    oldest_audio = self.audio_cache[oldest_path]
                    
                    # Stop and unload oldest audio
                    try:
                        if hasattr(oldest_audio, 'stop'):
                            oldest_audio.stop()
                        if hasattr(oldest_audio, 'unload'):
                            oldest_audio.unload()
                    except Exception as e:
                        logger.debug(f"Error unloading audio: {e}")
                    
                    del self.audio_cache[oldest_path]
                    logger.debug(f"Removed from cache: {os.path.basename(oldest_path)}")
                
                # Add to cache
                self.audio_cache[audio_path] = audio
                
        except Exception as e:
            logger.error(f"Error caching audio: {e}")
    
    def _cache_metadata(self, audio_path, metadata):
        """Cache audio metadata"""
        try:
            with self.cache_lock:
                # Limit metadata cache size
                while len(self.metadata_cache) >= 100:
                    oldest_key = next(iter(self.metadata_cache))
                    del self.metadata_cache[oldest_key]
                
                self.metadata_cache[audio_path] = metadata
                
        except Exception as e:
            logger.error(f"Error caching metadata: {e}")
    
    def _is_cached(self, audio_path):
        """Check if audio is already cached"""
        with self.cache_lock:
            return audio_path in self.audio_cache
    
    def _queue_preload(self, audio_paths):
        """Queue audio files for preloading"""
        try:
            if not isinstance(audio_paths, (list, tuple)):
                audio_paths = [audio_paths]
            
            with self.cache_lock:
                for path in audio_paths[:self.preload_count]:
                    if path and os.path.exists(path) and not self._is_cached(path):
                        if path not in self.preload_queue:
                            self.preload_queue.append(path)
            
            self.should_load.set()
            
        except Exception as e:
            logger.error(f"Error queuing preload: {e}")
    
    def _preload_audio(self, audio_path):
        """Preload a single audio file"""
        try:
            logger.debug(f"Preloading audio: {os.path.basename(audio_path)}")
            audio = self._load_audio_file(audio_path)
            
            if audio:
                self._cache_audio(audio_path, audio)
                logger.debug(f"Preloaded: {os.path.basename(audio_path)}")
            
        except Exception as e:
            logger.error(f"Error preloading audio {audio_path}: {e}")
    
    def preload_playlist_segment(self, playlist, current_index, window_size=3):
        """Preload a segment of the playlist around current position"""
        try:
            if not playlist or current_index < 0:
                return
            
            # Calculate preload window
            start_idx = max(0, current_index - window_size // 2)
            end_idx = min(len(playlist), current_index + window_size // 2 + 1)
            
            # Queue files for preloading
            preload_paths = []
            for i in range(start_idx, end_idx):
                if i != current_index and i < len(playlist):
                    preload_paths.append(playlist[i])
            
            if preload_paths:
                self._queue_preload(preload_paths)
                logger.debug(f"Queued {len(preload_paths)} files for preloading")
                
        except Exception as e:
            logger.error(f"Error preloading playlist segment: {e}")
    
    def get_next_audio(self, current_path, playlist, current_index):
        """Get next audio with intelligent preloading"""
        try:
            if not playlist or current_index >= len(playlist) - 1:
                return None
            
            next_path = playlist[current_index + 1]
            
            # Load next audio (will use cache if available)
            next_audio = self.load_audio_optimized(next_path)
            
            # Preload upcoming files
            upcoming_files = []
            for i in range(current_index + 2, min(current_index + 4, len(playlist))):
                upcoming_files.append(playlist[i])
            
            if upcoming_files:
                self._queue_preload(upcoming_files)
            
            return next_audio
            
        except Exception as e:
            logger.error(f"Error getting next audio: {e}")
            return None
    
    def _cleanup_old_cache(self):
        """Clean up old cache entries"""
        try:
            current_time = time.time()
            max_age = 1800  # 30 minutes
            
            with self.cache_lock:
                # Clean metadata cache
                keys_to_remove = []
                for path, metadata in self.metadata_cache.items():
                    if current_time - metadata['load_time'] > max_age:
                        keys_to_remove.append(path)
                
                for key in keys_to_remove:
                    del self.metadata_cache[key]
                
                # Don't clean audio cache automatically - let LRU handle it
                
        except Exception as e:
            logger.error(f"Error cleaning up cache: {e}")
    
    def get_audio_metadata(self, audio_path):
        """Get cached audio metadata"""
        try:
            with self.cache_lock:
                return self.metadata_cache.get(audio_path, {})
        except Exception as e:
            logger.error(f"Error getting metadata: {e}")
            return {}
    
    def get_cache_stats(self):
        """Get cache statistics"""
        try:
            with self.cache_lock:
                hit_rate = (self.cache_hits / (self.cache_hits + self.cache_misses) * 100 
                           if (self.cache_hits + self.cache_misses) > 0 else 0)
                
                avg_load_time = (sum(self.load_times) / len(self.load_times) 
                               if self.load_times else 0)
                
                return {
                    'cache_size': len(self.audio_cache),
                    'metadata_cache_size': len(self.metadata_cache),
                    'preload_queue_size': len(self.preload_queue),
                    'cache_hits': self.cache_hits,
                    'cache_misses': self.cache_misses,
                    'hit_rate_percent': hit_rate,
                    'avg_load_time_ms': avg_load_time * 1000,
                    'max_cache_size': self.max_cache_size
                }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}
    
    def clear_cache(self):
        """Clear all audio caches"""
        try:
            with self.cache_lock:
                # Stop and unload all cached audio
                for audio in self.audio_cache.values():
                    try:
                        if hasattr(audio, 'stop'):
                            audio.stop()
                        if hasattr(audio, 'unload'):
                            audio.unload()
                    except Exception as e:
                        logger.debug(f"Error unloading audio: {e}")
                
                self.audio_cache.clear()
                self.metadata_cache.clear()
                self.preload_queue.clear()
                
                # Reset stats
                self.cache_hits = 0
                self.cache_misses = 0
                self.load_times.clear()
            
            logger.info("Audio caches cleared")
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
    
    def optimize_for_device(self, device_type='medium'):
        """Optimize settings for device type"""
        try:
            if device_type == 'low':
                self.max_cache_size = 2
                self.preload_count = 1
                self.audio_buffer_size = 2048
                self.enable_preloading = False
            elif device_type == 'high':
                self.max_cache_size = 10
                self.preload_count = 3
                self.audio_buffer_size = 8192
                self.enable_preloading = True
            else:  # medium
                self.max_cache_size = 5
                self.preload_count = 2
                self.audio_buffer_size = 4096
                self.enable_preloading = True
            
            logger.info(f"Audio optimizer configured for {device_type} performance device")
            
        except Exception as e:
            logger.error(f"Error optimizing for device: {e}")
    
    def set_audio_quality(self, quality='balanced'):
        """Set audio quality settings"""
        try:
            if quality == 'low':
                self.audio_buffer_size = 1024
                self.preferred_format = 'mp3'
            elif quality == 'high':
                self.audio_buffer_size = 8192
                self.preferred_format = 'flac'
            else:  # balanced
                self.audio_buffer_size = 4096
                self.preferred_format = 'mp3'
            
            logger.info(f"Audio quality set to {quality}")
            
        except Exception as e:
            logger.error(f"Error setting audio quality: {e}")


class SmartAudioPlayer:
    """Smart audio player with optimization features"""
    
    def __init__(self, audio_optimizer=None):
        self.audio_optimizer = audio_optimizer or AudioOptimizer()
        self.current_audio = None
        self.current_path = None
        self.playlist = []
        self.current_index = -1
        
        # Playback state
        self.is_playing = False
        self.is_paused = False
        self.volume = 1.0
        self.position = 0
        
        # Performance tracking
        self.playback_start_time = None
        self.seek_times = deque(maxlen=10)
    
    def load_and_play(self, audio_path, playlist=None, index=-1):
        """Load and play audio with optimization"""
        try:
            self.playback_start_time = time.time()
            
            # Update playlist info
            if playlist:
                self.playlist = playlist
                self.current_index = index
            
            # Get next files for preloading
            next_files = []
            if self.playlist and self.current_index >= 0:
                for i in range(self.current_index + 1, min(self.current_index + 3, len(self.playlist))):
                    next_files.append(self.playlist[i])
            
            # Load audio with optimization
            audio = self.audio_optimizer.load_audio_optimized(audio_path, next_files)
            
            if audio:
                # Stop current audio
                self.stop()
                
                # Set new audio
                self.current_audio = audio
                self.current_path = audio_path
                
                # Apply settings
                audio.volume = self.volume
                
                # Start playback
                audio.play()
                self.is_playing = True
                self.is_paused = False
                
                # Preload playlist segment
                if self.playlist and self.current_index >= 0:
                    self.audio_optimizer.preload_playlist_segment(
                        self.playlist, self.current_index
                    )
                
                load_time = time.time() - self.playback_start_time
                logger.info(f"Audio playback started in {load_time:.3f}s: {os.path.basename(audio_path)}")
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error in load_and_play: {e}")
            return False
    
    def play_next(self):
        """Play next song with optimization"""
        try:
            if not self.playlist or self.current_index >= len(self.playlist) - 1:
                return False
            
            next_index = self.current_index + 1
            next_path = self.playlist[next_index]
            
            return self.load_and_play(next_path, self.playlist, next_index)
            
        except Exception as e:
            logger.error(f"Error playing next: {e}")
            return False
    
    def play_previous(self):
        """Play previous song"""
        try:
            if not self.playlist or self.current_index <= 0:
                return False
            
            prev_index = self.current_index - 1
            prev_path = self.playlist[prev_index]
            
            return self.load_and_play(prev_path, self.playlist, prev_index)
            
        except Exception as e:
            logger.error(f"Error playing previous: {e}")
            return False
    
    def pause(self):
        """Pause playback"""
        try:
            if self.current_audio and self.is_playing:
                self.current_audio.stop()
                self.is_playing = False
                self.is_paused = True
                return True
            return False
        except Exception as e:
            logger.error(f"Error pausing: {e}")
            return False
    
    def resume(self):
        """Resume playback"""
        try:
            if self.current_audio and self.is_paused:
                self.current_audio.play()
                self.is_playing = True
                self.is_paused = False
                return True
            return False
        except Exception as e:
            logger.error(f"Error resuming: {e}")
            return False
    
    def stop(self):
        """Stop playback"""
        try:
            if self.current_audio:
                self.current_audio.stop()
                self.is_playing = False
                self.is_paused = False
                return True
            return False
        except Exception as e:
            logger.error(f"Error stopping: {e}")
            return False
    
    def seek(self, position):
        """Seek to position with performance tracking"""
        try:
            start_time = time.time()
            
            if self.current_audio and hasattr(self.current_audio, 'seek'):
                self.current_audio.seek(position)
                self.position = position
                
                seek_time = time.time() - start_time
                self.seek_times.append(seek_time)
                
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error seeking: {e}")
            return False
    
    def set_volume(self, volume):
        """Set volume"""
        try:
            self.volume = max(0.0, min(1.0, volume))
            if self.current_audio:
                self.current_audio.volume = self.volume
            return True
        except Exception as e:
            logger.error(f"Error setting volume: {e}")
            return False
    
    def get_stats(self):
        """Get player statistics"""
        try:
            stats = self.audio_optimizer.get_cache_stats()
            
            avg_seek_time = (sum(self.seek_times) / len(self.seek_times) * 1000 
                           if self.seek_times else 0)
            
            stats.update({
                'current_song': os.path.basename(self.current_path) if self.current_path else None,
                'is_playing': self.is_playing,
                'is_paused': self.is_paused,
                'volume': self.volume,
                'avg_seek_time_ms': avg_seek_time,
                'playlist_size': len(self.playlist),
                'current_index': self.current_index
            })
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {}
