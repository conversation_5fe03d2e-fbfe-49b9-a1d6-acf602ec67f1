#!/usr/bin/env python3
"""
إعداد سريع لميزة التشغيل في الخلفية
Quick setup for background playback feature
"""

import os
import shutil
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_background_playback():
    """إعداد ميزة التشغيل في الخلفية"""
    logger.info("🚀 Setting up background playback feature...")
    
    print("🎵 Arabic Music Player - Background Playback Setup")
    print("=" * 55)
    
    # فحص الملفات المطلوبة
    required_files = [
        'background_service.py',
        'notification_receiver.py', 
        'background_test_app.py',
        'buildozer_background.spec',
        'android_background_manifest.xml'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            logger.info(f"✅ Found: {file}")
    
    if missing_files:
        logger.error(f"❌ Missing files: {missing_files}")
        return False
    
    print("\n📋 Setup Options:")
    print("1. Test background playback (recommended)")
    print("2. Add to existing main app")
    print("3. Create standalone background app")
    print("4. Exit")
    
    try:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            return setup_test_app()
        elif choice == "2":
            return add_to_main_app()
        elif choice == "3":
            return create_standalone_app()
        else:
            print("👋 Exiting...")
            return True
            
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Error in setup: {e}")
        return False

def setup_test_app():
    """إعداد تطبيق اختبار التشغيل في الخلفية"""
    logger.info("🧪 Setting up background playback test app...")
    
    try:
        # إنشاء نسخة احتياطية من buildozer.spec الحالي
        if os.path.exists('buildozer.spec'):
            backup_name = f"buildozer_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.spec"
            shutil.copy('buildozer.spec', backup_name)
            logger.info(f"📁 Backed up buildozer.spec to {backup_name}")
        
        # نسخ إعدادات التشغيل في الخلفية
        shutil.copy('buildozer_background.spec', 'buildozer.spec')
        logger.info("✅ Copied background buildozer configuration")
        
        # إنشاء مجلد للموسيقى إذا لم يكن موجوداً
        if not os.path.exists('music'):
            os.makedirs('music')
            logger.info("📁 Created music directory")
        
        print("\n🎉 Test app setup completed!")
        print("📋 Next steps:")
        print("   1. Add some music files to the 'music' directory")
        print("   2. Run: buildozer android clean")
        print("   3. Run: buildozer android debug")
        print("   4. Install and test the APK")
        print("\n🔍 Testing guide:")
        print("   - Open the app and play a song")
        print("   - Tap 'Enable Background' button")
        print("   - Tap 'Minimize App' to test background playback")
        print("   - Check notification controls")
        print("   - Test floating controls (if available)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to setup test app: {e}")
        return False

def add_to_main_app():
    """إضافة ميزة التشغيل في الخلفية للتطبيق الرئيسي"""
    logger.info("🔧 Adding background playback to main app...")
    
    try:
        # فحص وجود main.py
        if not os.path.exists('main.py'):
            logger.error("❌ main.py not found")
            return False
        
        # إنشاء نسخة احتياطية من main.py
        backup_name = f"main_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        shutil.copy('main.py', backup_name)
        logger.info(f"📁 Backed up main.py to {backup_name}")
        
        # قراءة main.py الحالي
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # إضافة استيراد مدير التشغيل في الخلفية
        background_import = """
# === BACKGROUND PLAYBACK IMPORTS ===
try:
    from notification_receiver import BackgroundPlaybackManager
    BACKGROUND_PLAYBACK_AVAILABLE = True
    logger.info("Background playback components imported successfully")
except ImportError as e:
    BACKGROUND_PLAYBACK_AVAILABLE = False
    logger.warning(f"Background playback not available: {e}")
    # Fallback class
    class BackgroundPlaybackManager:
        def __init__(self, app_instance=None):
            self.is_background_enabled = False
        def enable_background_playback(self, *args): return False
        def disable_background_playback(self): return False
        def update_background_info(self, *args): return False
        def cleanup(self): return False
"""
        
        # البحث عن مكان مناسب لإضافة الاستيراد
        if "# === SAFE ANDROID PERMISSIONS ===" in main_content:
            main_content = main_content.replace(
                "# === SAFE ANDROID PERMISSIONS ===",
                background_import + "\n# === SAFE ANDROID PERMISSIONS ==="
            )
        else:
            # إضافة في بداية الملف بعد الاستيرادات الأساسية
            lines = main_content.split('\n')
            insert_index = 0
            for i, line in enumerate(lines):
                if line.startswith('from kivy.') or line.startswith('import kivy'):
                    insert_index = i + 1
            
            lines.insert(insert_index, background_import)
            main_content = '\n'.join(lines)
        
        # كتابة الملف المحدث
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(main_content)
        
        # تحديث buildozer.spec للأذونات
        if os.path.exists('buildozer.spec'):
            with open('buildozer.spec', 'r') as f:
                spec_content = f.read()
            
            # إضافة الأذونات المطلوبة
            if 'android.permissions' in spec_content:
                # البحث عن سطر الأذونات وإضافة الأذونات الجديدة
                lines = spec_content.split('\n')
                for i, line in enumerate(lines):
                    if line.startswith('android.permissions'):
                        if 'FOREGROUND_SERVICE' not in line:
                            # إضافة الأذونات المطلوبة
                            current_perms = line.split('=')[1].strip()
                            new_perms = current_perms + ',FOREGROUND_SERVICE,WAKE_LOCK,SYSTEM_ALERT_WINDOW'
                            lines[i] = f'android.permissions = {new_perms}'
                        break
                
                spec_content = '\n'.join(lines)
                
                with open('buildozer.spec', 'w') as f:
                    f.write(spec_content)
                
                logger.info("✅ Updated buildozer.spec with background permissions")
        
        print("\n🎉 Background playback added to main app!")
        print("📋 Next steps:")
        print("   1. Review the updated main.py")
        print("   2. Add background playback controls to your UI")
        print("   3. Initialize BackgroundPlaybackManager in your app")
        print("   4. Test the integration")
        print("\n💡 Example usage:")
        print("   # In your app class:")
        print("   self.background_manager = BackgroundPlaybackManager(self)")
        print("   self.background_manager.enable_background_playback('Song Title', 'Artist')")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to add to main app: {e}")
        return False

def create_standalone_app():
    """إنشاء تطبيق مستقل للتشغيل في الخلفية"""
    logger.info("📱 Creating standalone background playback app...")
    
    try:
        # إنشاء مجلد للتطبيق المستقل
        app_dir = "arabic_player_background"
        if not os.path.exists(app_dir):
            os.makedirs(app_dir)
        
        # نسخ الملفات المطلوبة
        files_to_copy = [
            'background_service.py',
            'notification_receiver.py',
            'background_test_app.py',
            'buildozer_background.spec',
            'android_background_manifest.xml',
            'BACKGROUND_PLAYBACK_GUIDE.md'
        ]
        
        for file in files_to_copy:
            if os.path.exists(file):
                shutil.copy(file, os.path.join(app_dir, file))
                logger.info(f"✅ Copied {file}")
        
        # إنشاء main.py للتطبيق المستقل
        main_py_content = """#!/usr/bin/env python3
# Arabic Music Player - Background Playback Edition
from background_test_app import main

if __name__ == '__main__':
    main()
"""
        
        with open(os.path.join(app_dir, 'main.py'), 'w') as f:
            f.write(main_py_content)
        
        # نسخ buildozer.spec مع تعديل entrypoint
        shutil.copy('buildozer_background.spec', os.path.join(app_dir, 'buildozer.spec'))
        
        # إنشاء مجلد للموسيقى
        music_dir = os.path.join(app_dir, 'music')
        if not os.path.exists(music_dir):
            os.makedirs(music_dir)
        
        print(f"\n🎉 Standalone app created in '{app_dir}' directory!")
        print("📋 Next steps:")
        print(f"   1. cd {app_dir}")
        print("   2. Add music files to the 'music' directory")
        print("   3. buildozer android clean")
        print("   4. buildozer android debug")
        print("   5. Test the standalone background playback app")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create standalone app: {e}")
        return False

def check_requirements():
    """فحص المتطلبات"""
    logger.info("🔍 Checking requirements...")
    
    requirements = {
        'buildozer': 'buildozer --version',
        'adb': 'adb version',
        'python': 'python --version'
    }
    
    missing = []
    for tool, command in requirements.items():
        try:
            import subprocess
            result = subprocess.run(command.split(), capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ {tool} is available")
            else:
                missing.append(tool)
        except FileNotFoundError:
            missing.append(tool)
    
    if missing:
        logger.warning(f"⚠️ Missing tools: {missing}")
        print("💡 Install missing tools:")
        for tool in missing:
            if tool == 'buildozer':
                print("   pip install buildozer")
            elif tool == 'adb':
                print("   Install Android SDK Platform Tools")
    
    return len(missing) == 0

def main():
    """الدالة الرئيسية"""
    print("🎵 Arabic Music Player - Background Playback Setup")
    print("=" * 55)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n⚠️ Please install missing requirements first")
        return
    
    # إعداد التشغيل في الخلفية
    success = setup_background_playback()
    
    if success:
        print("\n🎉 Setup completed successfully!")
        print("📖 Read BACKGROUND_PLAYBACK_GUIDE.md for detailed testing instructions")
    else:
        print("\n❌ Setup failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
