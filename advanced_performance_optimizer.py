"""
Advanced Performance Optimizer for Music Player Application
This module provides advanced performance optimization techniques.
"""

import os
import time
import gc
import threading
import weakref
import logging
from collections import deque, OrderedDict
from kivy.clock import Clock
from kivy.cache import Cache
from kivy.core.window import Window
from kivy.utils import platform

logger = logging.getLogger(__name__)

class AdvancedPerformanceOptimizer:
    """Advanced performance optimizer with intelligent caching and memory management"""
    
    def __init__(self, app_instance):
        self.app = weakref.ref(app_instance)  # Use weak reference to prevent memory leaks
        
        # Performance metrics
        self.fps_history = deque(maxlen=60)  # Keep 60 FPS samples
        self.memory_history = deque(maxlen=30)  # Keep 30 memory samples
        self.load_times = deque(maxlen=20)  # Track loading times
        
        # Intelligent caching
        self.smart_cache = SmartCache(max_size=50)
        self.image_preloader = ImagePreloader()
        self.audio_cache = AudioCache(max_size=10)
        
        # Performance thresholds
        self.fps_target = 30
        self.memory_threshold = 80  # Percentage
        self.optimization_level = 2
        
        # Background optimization
        self.optimization_thread = None
        self.should_optimize = threading.Event()
        
        # Initialize components
        self._setup_caches()
        self._start_background_optimizer()
        
    def _setup_caches(self):
        """Setup Kivy caches with optimized settings"""
        try:
            # Configure Kivy caches
            Cache.register('kv.image', limit=20, timeout=300)  # 5 minutes
            Cache.register('kv.texture', limit=30, timeout=600)  # 10 minutes
            Cache.register('audio.metadata', limit=100, timeout=1800)  # 30 minutes
            
            logger.info("Advanced caches configured successfully")
        except Exception as e:
            logger.error(f"Error setting up caches: {e}")
    
    def _start_background_optimizer(self):
        """Start background optimization thread"""
        try:
            self.optimization_thread = threading.Thread(
                target=self._background_optimization_loop,
                daemon=True
            )
            self.optimization_thread.start()
            logger.info("Background optimizer started")
        except Exception as e:
            logger.error(f"Error starting background optimizer: {e}")
    
    def _background_optimization_loop(self):
        """Background optimization loop"""
        while True:
            try:
                # Wait for optimization signal or timeout
                if self.should_optimize.wait(timeout=30):
                    self.should_optimize.clear()
                    self._perform_background_optimization()
                else:
                    # Periodic optimization
                    self._perform_periodic_optimization()
                    
            except Exception as e:
                logger.error(f"Error in background optimization: {e}")
                time.sleep(5)  # Wait before retrying
    
    def _perform_background_optimization(self):
        """Perform intensive background optimization"""
        try:
            # Clean up caches
            self.smart_cache.cleanup()
            self.image_preloader.cleanup_old_images()
            self.audio_cache.cleanup()
            
            # Run garbage collection
            gc.collect()
            
            # Optimize app state
            app = self.app()
            if app and hasattr(app, 'root'):
                Clock.schedule_once(lambda dt: self._optimize_ui_elements(), 0)
                
        except Exception as e:
            logger.error(f"Error in background optimization: {e}")
    
    def _perform_periodic_optimization(self):
        """Perform light periodic optimization"""
        try:
            # Update performance metrics
            self._update_performance_metrics()
            
            # Adjust optimization level based on performance
            self._adjust_optimization_level()
            
            # Preload next items if performance is good
            if self._is_performance_good():
                self._preload_next_items()
                
        except Exception as e:
            logger.error(f"Error in periodic optimization: {e}")
    
    def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            app = self.app()
            if not app:
                return
                
            # Get current FPS
            current_fps = getattr(app, 'current_fps', 0)
            if current_fps > 0:
                self.fps_history.append(current_fps)
            
            # Get memory usage
            memory_usage = self._get_memory_usage()
            if memory_usage > 0:
                self.memory_history.append(memory_usage)
                
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    def _get_memory_usage(self):
        """Get current memory usage percentage"""
        try:
            if platform == 'android':
                return self._get_android_memory_usage()
            else:
                return self._get_desktop_memory_usage()
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return 50  # Default value
    
    def _get_android_memory_usage(self):
        """Get Android memory usage"""
        try:
            from jnius import autoclass
            ActivityManager = autoclass('android.app.ActivityManager')
            Context = autoclass('android.content.Context')
            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            
            activity = PythonActivity.mActivity
            am = activity.getSystemService(Context.ACTIVITY_SERVICE)
            mem_info = ActivityManager.MemoryInfo()
            am.getMemoryInfo(mem_info)
            
            return 100 - (mem_info.availMem * 100 / mem_info.totalMem)
        except Exception:
            return 50
    
    def _get_desktop_memory_usage(self):
        """Get desktop memory usage"""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except ImportError:
            return 50
    
    def _adjust_optimization_level(self):
        """Adjust optimization level based on performance"""
        try:
            if not self.fps_history or not self.memory_history:
                return
                
            avg_fps = sum(self.fps_history) / len(self.fps_history)
            avg_memory = sum(self.memory_history) / len(self.memory_history)
            
            # Determine new optimization level
            new_level = self.optimization_level
            
            if avg_fps < 20 or avg_memory > 85:
                new_level = min(3, self.optimization_level + 1)
            elif avg_fps > 40 and avg_memory < 60:
                new_level = max(1, self.optimization_level - 1)
            
            if new_level != self.optimization_level:
                self.optimization_level = new_level
                Clock.schedule_once(lambda dt: self._apply_optimization_level(), 0)
                logger.info(f"Adjusted optimization level to {new_level}")
                
        except Exception as e:
            logger.error(f"Error adjusting optimization level: {e}")
    
    def _apply_optimization_level(self):
        """Apply current optimization level"""
        try:
            app = self.app()
            if not app or not hasattr(app, 'root'):
                return
                
            root = app.root
            
            if self.optimization_level == 1:  # High performance
                self._apply_high_performance_settings(root)
            elif self.optimization_level == 2:  # Balanced
                self._apply_balanced_settings(root)
            else:  # Low performance
                self._apply_low_performance_settings(root)
                
        except Exception as e:
            logger.error(f"Error applying optimization level: {e}")
    
    def _apply_high_performance_settings(self, root):
        """Apply high performance settings"""
        try:
            # Enable all visual effects
            if hasattr(root, 'enable_animations'):
                root.enable_animations = True
            
            # Increase cache sizes
            self.smart_cache.max_size = 50
            self.audio_cache.max_size = 10
            
            # Faster update intervals
            if hasattr(root, '_progress_timer_interval'):
                root._progress_timer_interval = 0.1
                
        except Exception as e:
            logger.error(f"Error applying high performance settings: {e}")
    
    def _apply_balanced_settings(self, root):
        """Apply balanced settings"""
        try:
            # Moderate visual effects
            if hasattr(root, 'enable_animations'):
                root.enable_animations = True
            
            # Moderate cache sizes
            self.smart_cache.max_size = 30
            self.audio_cache.max_size = 7
            
            # Normal update intervals
            if hasattr(root, '_progress_timer_interval'):
                root._progress_timer_interval = 0.2
                
        except Exception as e:
            logger.error(f"Error applying balanced settings: {e}")
    
    def _apply_low_performance_settings(self, root):
        """Apply low performance settings"""
        try:
            # Disable or reduce visual effects
            if hasattr(root, 'enable_animations'):
                root.enable_animations = False
            
            # Reduce cache sizes
            self.smart_cache.max_size = 15
            self.audio_cache.max_size = 5
            
            # Slower update intervals
            if hasattr(root, '_progress_timer_interval'):
                root._progress_timer_interval = 0.5
                
            # Trigger immediate cleanup
            self.should_optimize.set()
            
        except Exception as e:
            logger.error(f"Error applying low performance settings: {e}")
    
    def _is_performance_good(self):
        """Check if current performance is good"""
        try:
            if not self.fps_history or not self.memory_history:
                return False
                
            recent_fps = list(self.fps_history)[-5:]  # Last 5 samples
            recent_memory = list(self.memory_history)[-3:]  # Last 3 samples
            
            avg_fps = sum(recent_fps) / len(recent_fps)
            avg_memory = sum(recent_memory) / len(recent_memory)
            
            return avg_fps >= self.fps_target and avg_memory < self.memory_threshold
        except Exception:
            return False
    
    def _preload_next_items(self):
        """Preload next items if performance allows"""
        try:
            app = self.app()
            if not app or not hasattr(app, 'root'):
                return
                
            # Schedule preloading in main thread
            Clock.schedule_once(lambda dt: self._do_preloading(), 0)
            
        except Exception as e:
            logger.error(f"Error preloading items: {e}")
    
    def _do_preloading(self):
        """Perform actual preloading"""
        try:
            app = self.app()
            if not app or not hasattr(app, 'root'):
                return
                
            root = app.root
            
            # Preload next song covers
            if hasattr(root, 'current_index') and hasattr(root, 'playlist'):
                current_index = getattr(root, 'current_index', -1)
                playlist = getattr(root, 'playlist', [])
                
                if 0 <= current_index < len(playlist) - 1:
                    next_song = playlist[current_index + 1]
                    self.image_preloader.preload_cover(next_song)
                    
        except Exception as e:
            logger.error(f"Error in preloading: {e}")
    
    def _optimize_ui_elements(self):
        """Optimize UI elements"""
        try:
            app = self.app()
            if not app or not hasattr(app, 'root'):
                return
                
            root = app.root
            
            # Clean up unused widgets
            if hasattr(root, 'ids') and hasattr(root.ids, 'playlist_list'):
                playlist_list = root.ids.playlist_list
                
                # Remove widgets that are far from view
                if hasattr(playlist_list, 'children'):
                    visible_range = 20  # Keep only 20 items around current view
                    children = list(playlist_list.children)
                    
                    if len(children) > visible_range * 2:
                        # Remove widgets that are too far from current view
                        for i, child in enumerate(children):
                            if i > visible_range and i < len(children) - visible_range:
                                if hasattr(child, 'cleanup'):
                                    child.cleanup()
                                    
        except Exception as e:
            logger.error(f"Error optimizing UI elements: {e}")
    
    def trigger_optimization(self):
        """Trigger immediate optimization"""
        self.should_optimize.set()
    
    def get_performance_stats(self):
        """Get current performance statistics"""
        try:
            stats = {
                'fps': sum(self.fps_history) / len(self.fps_history) if self.fps_history else 0,
                'memory': sum(self.memory_history) / len(self.memory_history) if self.memory_history else 0,
                'optimization_level': self.optimization_level,
                'cache_size': len(self.smart_cache),
                'preloaded_images': len(self.image_preloader),
                'audio_cache_size': len(self.audio_cache)
            }
            return stats
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {}


class SmartCache:
    """Intelligent cache with LRU eviction and size management"""
    
    def __init__(self, max_size=50):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.access_times = {}
        self.lock = threading.Lock()
    
    def get(self, key):
        with self.lock:
            if key in self.cache:
                # Move to end (most recently used)
                self.cache.move_to_end(key)
                self.access_times[key] = time.time()
                return self.cache[key]
            return None
    
    def set(self, key, value):
        with self.lock:
            if key in self.cache:
                self.cache.move_to_end(key)
            else:
                if len(self.cache) >= self.max_size:
                    # Remove least recently used
                    oldest_key = next(iter(self.cache))
                    del self.cache[oldest_key]
                    del self.access_times[oldest_key]
            
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def cleanup(self):
        """Remove old entries"""
        with self.lock:
            current_time = time.time()
            keys_to_remove = []
            
            for key, access_time in self.access_times.items():
                if current_time - access_time > 1800:  # 30 minutes
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                del self.access_times[key]
    
    def __len__(self):
        return len(self.cache)


class ImagePreloader:
    """Intelligent image preloader"""
    
    def __init__(self):
        self.preloaded = {}
        self.lock = threading.Lock()
    
    def preload_cover(self, song_path):
        """Preload album cover for a song"""
        try:
            with self.lock:
                if song_path not in self.preloaded:
                    # Start preloading in background
                    threading.Thread(
                        target=self._preload_image,
                        args=(song_path,),
                        daemon=True
                    ).start()
        except Exception as e:
            logger.error(f"Error preloading cover: {e}")
    
    def _preload_image(self, song_path):
        """Actually preload the image"""
        try:
            # This would be implemented based on your cover loading logic
            # For now, just mark as preloaded
            with self.lock:
                self.preloaded[song_path] = time.time()
        except Exception as e:
            logger.error(f"Error in image preloading: {e}")
    
    def cleanup_old_images(self):
        """Clean up old preloaded images"""
        with self.lock:
            current_time = time.time()
            keys_to_remove = []
            
            for path, load_time in self.preloaded.items():
                if current_time - load_time > 600:  # 10 minutes
                    keys_to_remove.append(path)
            
            for key in keys_to_remove:
                del self.preloaded[key]
    
    def __len__(self):
        return len(self.preloaded)


class AudioCache:
    """Cache for audio metadata and small audio files"""
    
    def __init__(self, max_size=10):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.Lock()
    
    def get(self, key):
        with self.lock:
            if key in self.cache:
                self.cache.move_to_end(key)
                return self.cache[key]
            return None
    
    def set(self, key, value):
        with self.lock:
            if len(self.cache) >= self.max_size:
                # Remove oldest
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
            
            self.cache[key] = value
    
    def cleanup(self):
        """Clean up cache"""
        with self.lock:
            # Keep only half the cache
            items_to_keep = self.max_size // 2
            if len(self.cache) > items_to_keep:
                keys = list(self.cache.keys())
                for key in keys[:-items_to_keep]:
                    del self.cache[key]
    
    def __len__(self):
        return len(self.cache)
