"""
نظام إدارة الاستقرار والأداء - Stability and Performance Management System
يوفر مراقبة شاملة للأخطاء والاستقرار وتحسين الأداء التلقائي
"""

import time
import threading
import gc
import logging
from kivy.clock import Clock
from kivy.cache import Cache

logger = logging.getLogger(__name__)

class StabilityManager:
    """مدير الاستقرار والأداء للتطبيق"""
    
    def __init__(self, app):
        self.app = app
        self.error_count = 0
        self.last_error_time = 0
        self.stability_mode = False
        self.performance_mode = 'auto'  # auto, low, medium, high
        self.memory_warnings = 0
        self.crash_recovery_enabled = True
        
        # إحصائيات الأداء
        self.performance_stats = {
            'errors_per_minute': 0,
            'memory_usage': 0,
            'response_time': 0,
            'stability_score': 100
        }
        
        # إعدادات التحسين
        self.optimization_settings = {
            'auto_gc_interval': 30,  # ثانية
            'memory_cleanup_threshold': 80,  # نسبة مئوية
            'error_threshold': 5,  # أخطاء في الدقيقة
            'stability_check_interval': 10  # ثانية
        }
        
        self._start_monitoring()
        logger.info("🛡️ Stability Manager initialized")

    def _start_monitoring(self):
        """بدء مراقبة الاستقرار"""
        try:
            # مراقبة دورية للاستقرار
            Clock.schedule_interval(
                self._check_stability, 
                self.optimization_settings['stability_check_interval']
            )
            
            # تنظيف الذاكرة التلقائي
            Clock.schedule_interval(
                self._auto_memory_cleanup,
                self.optimization_settings['auto_gc_interval']
            )
            
            logger.info("✅ Stability monitoring started")
        except Exception as e:
            logger.error(f"❌ Error starting stability monitoring: {e}")

    def report_error(self, error_type, error_message, severity='medium'):
        """تسجيل خطأ وتحديث إحصائيات الاستقرار"""
        try:
            current_time = time.time()
            self.error_count += 1
            
            # حساب معدل الأخطاء في الدقيقة
            time_diff = current_time - self.last_error_time
            if time_diff < 60:  # خلال دقيقة واحدة
                self.performance_stats['errors_per_minute'] += 1
            else:
                self.performance_stats['errors_per_minute'] = 1
                
            self.last_error_time = current_time
            
            # تحديث نقاط الاستقرار
            severity_impact = {'low': 1, 'medium': 3, 'high': 5}
            impact = severity_impact.get(severity, 3)
            self.performance_stats['stability_score'] = max(
                0, self.performance_stats['stability_score'] - impact
            )
            
            # تفعيل وضع الاستقرار إذا لزم الأمر
            if (self.performance_stats['errors_per_minute'] >= 
                self.optimization_settings['error_threshold']):
                self._enable_stability_mode()
                
            logger.warning(f"⚠️ Error reported: {error_type} - {error_message}")
            
        except Exception as e:
            logger.error(f"Error in error reporting: {e}")

    def _enable_stability_mode(self):
        """تفعيل وضع الاستقرار"""
        if not self.stability_mode:
            self.stability_mode = True
            self.performance_mode = 'low'
            
            # تطبيق إعدادات الاستقرار
            self._apply_stability_settings()
            
            logger.warning("🛡️ Stability mode enabled")

    def _disable_stability_mode(self):
        """إلغاء وضع الاستقرار"""
        if self.stability_mode:
            self.stability_mode = False
            self.performance_mode = 'auto'
            
            # استعادة الإعدادات العادية
            self._apply_normal_settings()
            
            logger.info("✅ Stability mode disabled")

    def _apply_stability_settings(self):
        """تطبيق إعدادات الاستقرار"""
        try:
            # تقليل تكرار العمليات
            self.optimization_settings['auto_gc_interval'] = 15
            self.optimization_settings['memory_cleanup_threshold'] = 60
            
            # تنظيف فوري للذاكرة
            self._force_memory_cleanup()
            
            # تقليل جودة الصور مؤقتاً
            if hasattr(self.app, 'image_optimizer'):
                self.app.image_optimizer.set_quality_mode('low')
                
            logger.info("🔧 Stability settings applied")
            
        except Exception as e:
            logger.error(f"Error applying stability settings: {e}")

    def _apply_normal_settings(self):
        """استعادة الإعدادات العادية"""
        try:
            # استعادة الإعدادات الافتراضية
            self.optimization_settings['auto_gc_interval'] = 30
            self.optimization_settings['memory_cleanup_threshold'] = 80
            
            # استعادة جودة الصور
            if hasattr(self.app, 'image_optimizer'):
                self.app.image_optimizer.set_quality_mode('auto')
                
            logger.info("🔧 Normal settings restored")
            
        except Exception as e:
            logger.error(f"Error restoring normal settings: {e}")

    def _check_stability(self, dt):
        """فحص دوري للاستقرار"""
        try:
            current_time = time.time()
            
            # إعادة تعيين عداد الأخطاء كل دقيقة
            if current_time - self.last_error_time > 60:
                self.performance_stats['errors_per_minute'] = 0
                
            # تحسين نقاط الاستقرار تدريجياً
            if self.performance_stats['errors_per_minute'] == 0:
                self.performance_stats['stability_score'] = min(
                    100, self.performance_stats['stability_score'] + 1
                )
                
            # إلغاء وضع الاستقرار إذا تحسن الوضع
            if (self.stability_mode and 
                self.performance_stats['stability_score'] > 90 and
                self.performance_stats['errors_per_minute'] == 0):
                self._disable_stability_mode()
                
            # مراقبة استهلاك الذاكرة
            self._check_memory_usage()
            
        except Exception as e:
            logger.error(f"Error in stability check: {e}")

    def _check_memory_usage(self):
        """مراقبة استهلاك الذاكرة"""
        try:
            # محاولة الحصول على معلومات الذاكرة
            memory_percent = 0
            
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_percent = process.memory_percent()
                self.performance_stats['memory_usage'] = memory_percent
            except ImportError:
                # استخدام تقدير تقريبي
                import sys
                memory_percent = len(gc.get_objects()) / 10000  # تقدير تقريبي
                
            # تحذير من استهلاك الذاكرة العالي
            if memory_percent > self.optimization_settings['memory_cleanup_threshold']:
                self.memory_warnings += 1
                if self.memory_warnings > 3:
                    self._force_memory_cleanup()
                    self.memory_warnings = 0
                    
        except Exception as e:
            logger.debug(f"Memory check error: {e}")

    def _auto_memory_cleanup(self, dt):
        """تنظيف تلقائي للذاكرة"""
        try:
            # تنظيف لطيف للذاكرة
            gc.collect()
            
            # تنظيف ذاكرة التخزين المؤقت للصور
            try:
                Cache.remove('kv.image')
                Cache.remove('kv.texture')
            except:
                pass
                
            # تنظيف ذاكرة التخزين المؤقت للتطبيق
            if hasattr(self.app, '_cover_cache'):
                cache_size = len(self.app._cover_cache)
                if cache_size > 50:  # حد أقصى 50 صورة
                    # إزالة النصف الأقدم
                    items = list(self.app._cover_cache.items())
                    for key, _ in items[:cache_size//2]:
                        del self.app._cover_cache[key]
                        
            logger.debug("🧹 Auto memory cleanup completed")
            
        except Exception as e:
            logger.error(f"Error in auto memory cleanup: {e}")

    def _force_memory_cleanup(self):
        """تنظيف قوي للذاكرة"""
        try:
            # تنظيف قوي
            for _ in range(3):
                gc.collect()
                
            # تنظيف جميع ذاكرات التخزين المؤقت
            try:
                Cache.remove('kv.image')
                Cache.remove('kv.texture')
                Cache.remove('kv.loader')
            except:
                pass
                
            # تنظيف ذاكرة التطبيق
            if hasattr(self.app, '_cover_cache'):
                self.app._cover_cache.clear()
            if hasattr(self.app, '_metadata_cache'):
                self.app._metadata_cache.clear()
                
            logger.info("🧹 Force memory cleanup completed")
            
        except Exception as e:
            logger.error(f"Error in force memory cleanup: {e}")

    def get_stability_report(self):
        """الحصول على تقرير الاستقرار"""
        return {
            'stability_mode': self.stability_mode,
            'performance_mode': self.performance_mode,
            'error_count': self.error_count,
            'memory_warnings': self.memory_warnings,
            'performance_stats': self.performance_stats.copy(),
            'optimization_settings': self.optimization_settings.copy()
        }

    def optimize_for_device(self, device_type='auto'):
        """تحسين الإعدادات حسب نوع الجهاز"""
        try:
            if device_type == 'low_end':
                self.optimization_settings['auto_gc_interval'] = 20
                self.optimization_settings['memory_cleanup_threshold'] = 60
                self.performance_mode = 'low'
            elif device_type == 'high_end':
                self.optimization_settings['auto_gc_interval'] = 45
                self.optimization_settings['memory_cleanup_threshold'] = 90
                self.performance_mode = 'high'
            else:  # auto or medium
                self.optimization_settings['auto_gc_interval'] = 30
                self.optimization_settings['memory_cleanup_threshold'] = 80
                self.performance_mode = 'medium'
                
            logger.info(f"🔧 Optimized for {device_type} device")
            
        except Exception as e:
            logger.error(f"Error optimizing for device: {e}")

    def handle_crash_recovery(self):
        """معالجة استعادة التطبيق بعد التعطل"""
        if not self.crash_recovery_enabled:
            return
            
        try:
            logger.info("🔄 Starting crash recovery...")
            
            # تفعيل وضع الاستقرار
            self._enable_stability_mode()
            
            # تنظيف شامل للذاكرة
            self._force_memory_cleanup()
            
            # إعادة تعيين الإحصائيات
            self.error_count = 0
            self.memory_warnings = 0
            self.performance_stats['stability_score'] = 70  # نقطة بداية متحفظة
            
            logger.info("✅ Crash recovery completed")
            
        except Exception as e:
            logger.error(f"Error in crash recovery: {e}")

    def shutdown(self):
        """إيقاف مدير الاستقرار"""
        try:
            # إلغاء جميع المهام المجدولة
            Clock.unschedule(self._check_stability)
            Clock.unschedule(self._auto_memory_cleanup)
            
            # تنظيف نهائي
            self._force_memory_cleanup()
            
            logger.info("🛡️ Stability Manager shutdown completed")
            
        except Exception as e:
            logger.error(f"Error in stability manager shutdown: {e}")
