"""
تحليلات البحث - Search Analytics
يوفر تحليلات مفصلة لاستخدام البحث وتحسين تجربة المستخدم
"""

import os
import json
import time
import logging
from collections import defaultdict, Counter
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class SearchAnalytics:
    """نظام تحليلات البحث"""
    
    def __init__(self, app):
        self.app = app
        
        # بيانات التحليلات
        self.analytics_data = {
            'search_history': [],
            'popular_searches': defaultdict(int),
            'search_patterns': defaultdict(int),
            'user_preferences': defaultdict(int),
            'performance_metrics': {
                'total_searches': 0,
                'successful_searches': 0,
                'empty_results': 0,
                'average_search_time': 0,
                'peak_usage_hours': defaultdict(int)
            }
        }
        
        # إعدادات التحليلات
        self.analytics_settings = {
            'track_searches': True,
            'save_history': True,
            'max_history_size': 1000,
            'analytics_file': 'search_analytics.json',
            'auto_save_interval': 300  # 5 دقائق
        }
        
        # تحميل البيانات المحفوظة
        self._load_analytics_data()
        
        logger.info("📊 Search Analytics initialized")

    def track_search(self, query, results_count, search_time, search_type='enhanced'):
        """تتبع عملية بحث"""
        try:
            if not self.analytics_settings['track_searches']:
                return
            
            current_time = datetime.now()
            
            # تسجيل البحث في التاريخ
            search_record = {
                'query': query,
                'results_count': results_count,
                'search_time': search_time,
                'search_type': search_type,
                'timestamp': current_time.isoformat(),
                'hour': current_time.hour,
                'day_of_week': current_time.weekday()
            }
            
            if self.analytics_settings['save_history']:
                self.analytics_data['search_history'].append(search_record)
                
                # تحديد حجم التاريخ
                max_size = self.analytics_settings['max_history_size']
                if len(self.analytics_data['search_history']) > max_size:
                    self.analytics_data['search_history'] = self.analytics_data['search_history'][-max_size:]
            
            # تحديث الإحصائيات
            self._update_search_metrics(query, results_count, search_time, current_time)
            
            # تحليل أنماط البحث
            self._analyze_search_patterns(query, results_count)
            
            logger.debug(f"📊 Tracked search: '{query}' -> {results_count} results in {search_time:.3f}s")
            
        except Exception as e:
            logger.error(f"Error tracking search: {e}")

    def _update_search_metrics(self, query, results_count, search_time, timestamp):
        """تحديث مقاييس البحث"""
        try:
            metrics = self.analytics_data['performance_metrics']
            
            # تحديث العدادات
            metrics['total_searches'] += 1
            
            if results_count > 0:
                metrics['successful_searches'] += 1
            else:
                metrics['empty_results'] += 1
            
            # تحديث متوسط وقت البحث
            if metrics['average_search_time'] == 0:
                metrics['average_search_time'] = search_time
            else:
                metrics['average_search_time'] = (
                    metrics['average_search_time'] * 0.9 + search_time * 0.1
                )
            
            # تتبع ساعات الذروة
            metrics['peak_usage_hours'][timestamp.hour] += 1
            
            # تحديث البحثات الشائعة
            if query and len(query.strip()) > 1:
                self.analytics_data['popular_searches'][query.lower()] += 1
            
        except Exception as e:
            logger.error(f"Error updating search metrics: {e}")

    def _analyze_search_patterns(self, query, results_count):
        """تحليل أنماط البحث"""
        try:
            # تحليل طول الاستعلام
            query_length = len(query.split()) if query else 0
            self.analytics_data['search_patterns'][f'query_length_{query_length}'] += 1
            
            # تحليل نوع النتائج
            if results_count == 0:
                self.analytics_data['search_patterns']['no_results'] += 1
            elif results_count < 5:
                self.analytics_data['search_patterns']['few_results'] += 1
            elif results_count < 20:
                self.analytics_data['search_patterns']['moderate_results'] += 1
            else:
                self.analytics_data['search_patterns']['many_results'] += 1
            
            # تحليل نوع المحتوى المبحوث عنه
            if query:
                query_lower = query.lower()
                
                # كلمات مفتاحية للأنواع الموسيقية
                genre_keywords = {
                    'rock': ['rock', 'metal', 'punk'],
                    'pop': ['pop', 'dance', 'hit'],
                    'classical': ['classical', 'symphony', 'opera'],
                    'jazz': ['jazz', 'blues', 'swing'],
                    'electronic': ['electronic', 'techno', 'house', 'edm'],
                    'arabic': ['عربي', 'arabic', 'شرقي', 'طرب']
                }
                
                for genre, keywords in genre_keywords.items():
                    if any(keyword in query_lower for keyword in keywords):
                        self.analytics_data['user_preferences'][f'genre_{genre}'] += 1
                        break
                
                # كلمات مفتاحية للفنانين
                if any(word in query_lower for word in ['singer', 'artist', 'مطرب', 'فنان']):
                    self.analytics_data['user_preferences']['search_by_artist'] += 1
                
                # كلمات مفتاحية للألبومات
                if any(word in query_lower for word in ['album', 'ألبوم']):
                    self.analytics_data['user_preferences']['search_by_album'] += 1
            
        except Exception as e:
            logger.error(f"Error analyzing search patterns: {e}")

    def get_popular_searches(self, limit=10):
        """الحصول على البحثات الأكثر شيوعاً"""
        try:
            popular = Counter(self.analytics_data['popular_searches'])
            return popular.most_common(limit)
        except Exception as e:
            logger.error(f"Error getting popular searches: {e}")
            return []

    def get_search_suggestions_based_on_history(self, query, limit=5):
        """الحصول على اقتراحات بناءً على التاريخ"""
        try:
            if not query or len(query) < 2:
                return []
            
            query_lower = query.lower()
            suggestions = []
            
            # البحث في التاريخ
            for search_record in reversed(self.analytics_data['search_history']):
                search_query = search_record['query'].lower()
                if (search_query.startswith(query_lower) and 
                    search_query != query_lower and
                    search_record['results_count'] > 0):
                    
                    if search_record['query'] not in suggestions:
                        suggestions.append(search_record['query'])
                        
                    if len(suggestions) >= limit:
                        break
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting history-based suggestions: {e}")
            return []

    def get_analytics_report(self):
        """الحصول على تقرير التحليلات"""
        try:
            metrics = self.analytics_data['performance_metrics']
            
            # حساب معدل النجاح
            success_rate = 0
            if metrics['total_searches'] > 0:
                success_rate = (metrics['successful_searches'] / metrics['total_searches']) * 100
            
            # العثور على ساعة الذروة
            peak_hour = 0
            if metrics['peak_usage_hours']:
                peak_hour = max(metrics['peak_usage_hours'], key=metrics['peak_usage_hours'].get)
            
            # أهم البحثات
            top_searches = self.get_popular_searches(5)
            
            # أنماط البحث الأكثر شيوعاً
            common_patterns = Counter(self.analytics_data['search_patterns']).most_common(5)
            
            # تفضيلات المستخدم
            user_prefs = Counter(self.analytics_data['user_preferences']).most_common(5)
            
            report = {
                'summary': {
                    'total_searches': metrics['total_searches'],
                    'success_rate': f"{success_rate:.1f}%",
                    'average_search_time': f"{metrics['average_search_time']:.3f}s",
                    'peak_usage_hour': f"{peak_hour}:00",
                    'history_size': len(self.analytics_data['search_history'])
                },
                'popular_searches': top_searches,
                'search_patterns': common_patterns,
                'user_preferences': user_prefs,
                'recent_activity': self._get_recent_activity()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating analytics report: {e}")
            return {}

    def _get_recent_activity(self, hours=24):
        """الحصول على النشاط الأخير"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_searches = []
            
            for search_record in reversed(self.analytics_data['search_history']):
                search_time = datetime.fromisoformat(search_record['timestamp'])
                if search_time >= cutoff_time:
                    recent_searches.append({
                        'query': search_record['query'],
                        'results': search_record['results_count'],
                        'time': search_record['timestamp']
                    })
                else:
                    break
            
            return recent_searches[:10]  # آخر 10 بحثات
            
        except Exception as e:
            logger.error(f"Error getting recent activity: {e}")
            return []

    def optimize_search_based_on_analytics(self):
        """تحسين البحث بناءً على التحليلات"""
        try:
            recommendations = []
            
            metrics = self.analytics_data['performance_metrics']
            
            # تحليل معدل النجاح
            if metrics['total_searches'] > 10:
                success_rate = (metrics['successful_searches'] / metrics['total_searches']) * 100
                
                if success_rate < 70:
                    recommendations.append("Consider improving fuzzy search sensitivity")
                
                if metrics['empty_results'] > metrics['successful_searches']:
                    recommendations.append("Many searches return no results - expand search criteria")
            
            # تحليل أوقات البحث
            if metrics['average_search_time'] > 1.0:
                recommendations.append("Search performance is slow - consider optimizing search index")
            
            # تحليل أنماط البحث
            patterns = self.analytics_data['search_patterns']
            if patterns.get('query_length_1', 0) > patterns.get('query_length_2', 0):
                recommendations.append("Users prefer short queries - improve single-word search")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error optimizing search: {e}")
            return []

    def _save_analytics_data(self):
        """حفظ بيانات التحليلات"""
        try:
            if not self.analytics_settings['save_history']:
                return
            
            file_path = self.analytics_settings['analytics_file']
            
            # تحويل defaultdict إلى dict عادي للحفظ
            save_data = {
                'search_history': self.analytics_data['search_history'],
                'popular_searches': dict(self.analytics_data['popular_searches']),
                'search_patterns': dict(self.analytics_data['search_patterns']),
                'user_preferences': dict(self.analytics_data['user_preferences']),
                'performance_metrics': {
                    **self.analytics_data['performance_metrics'],
                    'peak_usage_hours': dict(self.analytics_data['performance_metrics']['peak_usage_hours'])
                }
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            logger.debug("💾 Search analytics data saved")
            
        except Exception as e:
            logger.error(f"Error saving analytics data: {e}")

    def _load_analytics_data(self):
        """تحميل بيانات التحليلات المحفوظة"""
        try:
            file_path = self.analytics_settings['analytics_file']
            
            if not os.path.exists(file_path):
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
            
            # استعادة البيانات
            self.analytics_data['search_history'] = loaded_data.get('search_history', [])
            self.analytics_data['popular_searches'] = defaultdict(int, loaded_data.get('popular_searches', {}))
            self.analytics_data['search_patterns'] = defaultdict(int, loaded_data.get('search_patterns', {}))
            self.analytics_data['user_preferences'] = defaultdict(int, loaded_data.get('user_preferences', {}))
            
            # استعادة مقاييس الأداء
            metrics = loaded_data.get('performance_metrics', {})
            self.analytics_data['performance_metrics'].update(metrics)
            
            if 'peak_usage_hours' in metrics:
                self.analytics_data['performance_metrics']['peak_usage_hours'] = defaultdict(
                    int, metrics['peak_usage_hours']
                )
            
            logger.info(f"📊 Loaded {len(self.analytics_data['search_history'])} search records")
            
        except Exception as e:
            logger.error(f"Error loading analytics data: {e}")

    def clear_analytics_data(self):
        """مسح بيانات التحليلات"""
        try:
            self.analytics_data = {
                'search_history': [],
                'popular_searches': defaultdict(int),
                'search_patterns': defaultdict(int),
                'user_preferences': defaultdict(int),
                'performance_metrics': {
                    'total_searches': 0,
                    'successful_searches': 0,
                    'empty_results': 0,
                    'average_search_time': 0,
                    'peak_usage_hours': defaultdict(int)
                }
            }
            
            # حذف الملف المحفوظ
            file_path = self.analytics_settings['analytics_file']
            if os.path.exists(file_path):
                os.remove(file_path)
            
            logger.info("🧹 Search analytics data cleared")
            
        except Exception as e:
            logger.error(f"Error clearing analytics data: {e}")

    def shutdown(self):
        """إيقاف نظام التحليلات"""
        try:
            # حفظ البيانات قبل الإغلاق
            self._save_analytics_data()
            logger.info("📊 Search Analytics shutdown")
        except Exception as e:
            logger.error(f"Error shutting down analytics: {e}")

# دالة مساعدة لتطبيق النظام
def apply_search_analytics(app):
    """تطبيق نظام تحليلات البحث على التطبيق"""
    try:
        if not hasattr(app, 'search_analytics'):
            app.search_analytics = SearchAnalytics(app)
            logger.info("📊 Search Analytics applied to app")
        return app.search_analytics
    except Exception as e:
        logger.error(f"Failed to apply search analytics: {e}")
        return None
