#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the enhanced background system in the music player
"""

import os
import sys
import logging

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_background_functions():
    """Test the background-related functions"""
    try:
        # Import the main module
        from main import MusicPlayer
        from kivymd.app import MDApp
        
        # Create a simple test app
        class TestApp(MDApp):
            def build(self):
                self.theme_cls.primary_palette = "DeepPurple"
                self.theme_cls.theme_style = "Dark"
                return MusicPlayer()
        
        # Create app instance
        app = TestApp()
        player = app.build()
        
        # Test background color functions
        logger.info("Testing background color functions...")
        
        # Test basic background color
        bg_color = player.get_bg_color()
        logger.info(f"Basic background color: {bg_color}")
        
        # Test gradient colors
        gradient_colors = player.get_gradient_bg_colors()
        logger.info(f"Gradient colors: {gradient_colors}")
        
        # Test dynamic background color
        dynamic_color = player.get_dynamic_bg_color()
        logger.info(f"Dynamic background color: {dynamic_color}")
        
        # Test pattern background color
        pattern_colors = player.get_pattern_bg_color()
        logger.info(f"Pattern background colors: {pattern_colors}")
        
        logger.info("✅ All background functions tested successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing background functions: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_background_widgets():
    """Test the background widget classes"""
    try:
        from main import GradientBackground, PatternBackground
        from kivy.metrics import dp
        
        logger.info("Testing background widget classes...")
        
        # Test GradientBackground
        gradient_bg = GradientBackground()
        gradient_bg.colors = [[0.1, 0.1, 0.2, 1], [0.2, 0.2, 0.4, 1]]
        gradient_bg.direction = 'vertical'
        gradient_bg.size = (dp(300), dp(400))
        logger.info("✅ GradientBackground created successfully")
        
        # Test PatternBackground
        pattern_bg = PatternBackground()
        pattern_bg.base_color = [0.1, 0.1, 0.1, 1]
        pattern_bg.pattern_color = [0.3, 0.3, 0.3, 0.5]
        pattern_bg.pattern_type = 'dots'
        pattern_bg.size = (dp(300), dp(400))
        logger.info("✅ PatternBackground created successfully")
        
        logger.info("✅ All background widgets tested successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing background widgets: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main test function"""
    logger.info("🎨 Starting background system tests...")
    
    # Test 1: Background functions
    logger.info("\n📋 Test 1: Background Functions")
    test1_result = test_background_functions()
    
    # Test 2: Background widgets
    logger.info("\n📋 Test 2: Background Widgets")
    test2_result = test_background_widgets()
    
    # Summary
    logger.info("\n📊 Test Summary:")
    logger.info(f"   Background Functions: {'✅ PASS' if test1_result else '❌ FAIL'}")
    logger.info(f"   Background Widgets: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        logger.info("\n🎉 All tests passed! Background system is working correctly.")
        return True
    else:
        logger.error("\n💥 Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
