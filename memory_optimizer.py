"""
Memory Optimizer for Music Player Application
Provides intelligent memory management and garbage collection.
"""

import gc
import time
import threading
import logging
import weakref
from collections import defaultdict
from kivy.clock import Clock
from kivy.cache import Cache

logger = logging.getLogger(__name__)

class MemoryOptimizer:
    """Advanced memory optimization and management system"""
    
    def __init__(self, app_instance, target_memory_mb=150):
        self.app = weakref.ref(app_instance)
        self.target_memory_mb = target_memory_mb
        self.current_memory_mb = 0
        
        # Memory tracking
        self.memory_history = []
        self.gc_history = []
        self.last_gc_time = time.time()
        
        # Object tracking
        self.tracked_objects = defaultdict(list)
        self.object_sizes = {}
        
        # Optimization settings
        self.gc_threshold = 0.8  # Trigger GC at 80% of target
        self.aggressive_threshold = 0.9  # Aggressive cleanup at 90%
        self.gc_interval = 30  # Minimum seconds between GC
        
        # Background optimization
        self.optimization_thread = None
        self.should_optimize = threading.Event()
        self.running = True
        
        self._start_memory_monitor()
    
    def _start_memory_monitor(self):
        """Start background memory monitoring"""
        try:
            self.optimization_thread = threading.Thread(
                target=self._memory_monitoring_loop,
                daemon=True
            )
            self.optimization_thread.start()
            logger.info("Memory monitor started")
        except Exception as e:
            logger.error(f"Error starting memory monitor: {e}")
    
    def _memory_monitoring_loop(self):
        """Background memory monitoring loop"""
        while self.running:
            try:
                # Update memory usage
                self._update_memory_usage()
                
                # Check if optimization is needed
                if self._should_optimize():
                    self._perform_memory_optimization()
                
                # Wait before next check
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                time.sleep(5)
    
    def _update_memory_usage(self):
        """Update current memory usage"""
        try:
            # Get memory usage from system
            memory_mb = self._get_system_memory_usage()
            
            if memory_mb > 0:
                self.current_memory_mb = memory_mb
                self.memory_history.append({
                    'time': time.time(),
                    'memory_mb': memory_mb
                })
                
                # Keep only recent history
                if len(self.memory_history) > 100:
                    self.memory_history = self.memory_history[-50:]
                    
        except Exception as e:
            logger.error(f"Error updating memory usage: {e}")
    
    def _get_system_memory_usage(self):
        """Get system memory usage"""
        try:
            # Try to get process memory usage
            try:
                import psutil
                import os
                process = psutil.Process(os.getpid())
                return process.memory_info().rss / (1024 * 1024)  # MB
            except ImportError:
                # Fallback for Android or systems without psutil
                return self._estimate_memory_usage()
        except Exception as e:
            logger.debug(f"Error getting system memory: {e}")
            return 0
    
    def _estimate_memory_usage(self):
        """Estimate memory usage based on tracked objects"""
        try:
            total_size = 0
            
            # Estimate based on tracked objects
            for obj_type, objects in self.tracked_objects.items():
                for obj_ref in objects[:]:  # Copy list to avoid modification during iteration
                    obj = obj_ref()
                    if obj is None:
                        # Object was garbage collected
                        objects.remove(obj_ref)
                    else:
                        # Estimate object size
                        size = self.object_sizes.get(id(obj), 1)  # Default 1MB
                        total_size += size
            
            return total_size
            
        except Exception as e:
            logger.error(f"Error estimating memory: {e}")
            return 50  # Default estimate
    
    def _should_optimize(self):
        """Check if memory optimization is needed"""
        try:
            if not self.memory_history:
                return False
            
            current_usage = self.current_memory_mb
            usage_ratio = current_usage / self.target_memory_mb
            
            # Check if we're above threshold
            if usage_ratio >= self.gc_threshold:
                return True
            
            # Check if memory is growing rapidly
            if len(self.memory_history) >= 5:
                recent_usage = [h['memory_mb'] for h in self.memory_history[-5:]]
                if len(set(recent_usage)) > 1:  # Memory is changing
                    growth_rate = (recent_usage[-1] - recent_usage[0]) / len(recent_usage)
                    if growth_rate > 5:  # Growing by more than 5MB per check
                        return True
            
            # Check minimum time since last GC
            time_since_gc = time.time() - self.last_gc_time
            if time_since_gc < self.gc_interval:
                return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking optimization need: {e}")
            return False
    
    def _perform_memory_optimization(self):
        """Perform memory optimization"""
        try:
            start_time = time.time()
            start_memory = self.current_memory_mb
            
            logger.info(f"🧹 Starting memory optimization (current: {start_memory:.1f}MB)")
            
            # Determine optimization level
            usage_ratio = self.current_memory_mb / self.target_memory_mb
            
            if usage_ratio >= self.aggressive_threshold:
                self._aggressive_cleanup()
            else:
                self._gentle_cleanup()
            
            # Run garbage collection
            self._run_garbage_collection()
            
            # Update memory after optimization
            Clock.schedule_once(lambda dt: self._update_memory_usage(), 1)
            
            optimization_time = time.time() - start_time
            self.last_gc_time = time.time()
            
            logger.info(f"✅ Memory optimization completed in {optimization_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Error in memory optimization: {e}")
    
    def _gentle_cleanup(self):
        """Perform gentle memory cleanup"""
        try:
            app = self.app()
            if not app:
                return
            
            # Clean Kivy caches
            self._clean_kivy_caches(aggressive=False)
            
            # Clean app caches
            self._clean_app_caches(app, aggressive=False)
            
            # Clean tracked objects
            self._clean_tracked_objects(aggressive=False)
            
        except Exception as e:
            logger.error(f"Error in gentle cleanup: {e}")
    
    def _aggressive_cleanup(self):
        """Perform aggressive memory cleanup"""
        try:
            app = self.app()
            if not app:
                return
            
            logger.warning("🚨 Performing aggressive memory cleanup")
            
            # Clean Kivy caches aggressively
            self._clean_kivy_caches(aggressive=True)
            
            # Clean app caches aggressively
            self._clean_app_caches(app, aggressive=True)
            
            # Clean tracked objects aggressively
            self._clean_tracked_objects(aggressive=True)
            
            # Clear unused widgets
            self._clean_unused_widgets(app)
            
        except Exception as e:
            logger.error(f"Error in aggressive cleanup: {e}")
    
    def _clean_kivy_caches(self, aggressive=False):
        """Clean Kivy internal caches"""
        try:
            if aggressive:
                # Clear all caches
                Cache.remove('kv.image')
                Cache.remove('kv.texture')
                Cache.remove('kv.loader')
            else:
                # Clean only old entries
                for cache_name in ['kv.image', 'kv.texture']:
                    try:
                        cache = Cache.get_cache(cache_name)
                        if cache and hasattr(cache, '_data'):
                            # Remove half of the entries
                            items = list(cache._data.items())
                            for key, _ in items[:len(items)//2]:
                                cache.remove(key)
                    except Exception as e:
                        logger.debug(f"Error cleaning cache {cache_name}: {e}")
                        
        except Exception as e:
            logger.error(f"Error cleaning Kivy caches: {e}")
    
    def _clean_app_caches(self, app, aggressive=False):
        """Clean application-specific caches"""
        try:
            # Clean cover cache
            if hasattr(app, '_cover_cache'):
                cache_size = len(app._cover_cache)
                if aggressive:
                    app._cover_cache.clear()
                    logger.debug(f"Cleared cover cache ({cache_size} items)")
                else:
                    # Keep only recent entries
                    items = list(app._cover_cache.items())
                    keep_count = max(10, cache_size // 2)
                    app._cover_cache = dict(items[-keep_count:])
                    logger.debug(f"Reduced cover cache from {cache_size} to {len(app._cover_cache)} items")
            
            # Clean title cache
            if hasattr(app, '_title_cache'):
                cache_size = len(app._title_cache)
                if aggressive:
                    app._title_cache.clear()
                else:
                    items = list(app._title_cache.items())
                    keep_count = max(50, cache_size // 2)
                    app._title_cache = dict(items[-keep_count:])
                logger.debug(f"Cleaned title cache: {cache_size} -> {len(app._title_cache)}")
            
            # Clean optimizer caches
            if hasattr(app, 'image_optimizer') and app.image_optimizer:
                if aggressive:
                    app.image_optimizer.clear_cache()
                else:
                    # Trigger cleanup
                    app.image_optimizer._cleanup_old_cache()
            
            if hasattr(app, 'audio_optimizer') and app.audio_optimizer:
                if aggressive:
                    app.audio_optimizer.clear_cache()
                else:
                    # Trigger cleanup
                    app.audio_optimizer._cleanup_old_cache()
                    
        except Exception as e:
            logger.error(f"Error cleaning app caches: {e}")
    
    def _clean_tracked_objects(self, aggressive=False):
        """Clean tracked objects"""
        try:
            total_cleaned = 0
            
            for obj_type, objects in self.tracked_objects.items():
                initial_count = len(objects)
                
                # Remove dead references
                objects[:] = [ref for ref in objects if ref() is not None]
                
                if aggressive:
                    # Remove more objects
                    keep_count = max(5, len(objects) // 3)
                    objects[:] = objects[-keep_count:]
                
                cleaned = initial_count - len(objects)
                total_cleaned += cleaned
                
                if cleaned > 0:
                    logger.debug(f"Cleaned {cleaned} {obj_type} objects")
            
            if total_cleaned > 0:
                logger.info(f"🧹 Cleaned {total_cleaned} tracked objects")
                
        except Exception as e:
            logger.error(f"Error cleaning tracked objects: {e}")
    
    def _clean_unused_widgets(self, app):
        """Clean unused widgets"""
        try:
            if not hasattr(app, 'root') or not app.root:
                return
            
            # Clean playlist items that are not visible
            if hasattr(app.root, 'ids') and hasattr(app.root.ids, 'playlist_list'):
                playlist_list = app.root.ids.playlist_list
                
                if hasattr(playlist_list, 'children'):
                    children_count = len(playlist_list.children)
                    
                    # Keep only visible items plus some buffer
                    visible_count = 20  # Approximate visible items
                    buffer_count = 10   # Buffer for smooth scrolling
                    max_keep = visible_count + buffer_count
                    
                    if children_count > max_keep:
                        # Remove excess children
                        excess_children = playlist_list.children[max_keep:]
                        for child in excess_children:
                            try:
                                playlist_list.remove_widget(child)
                            except Exception as e:
                                logger.debug(f"Error removing widget: {e}")
                        
                        logger.info(f"🧹 Removed {len(excess_children)} excess playlist items")
                        
        except Exception as e:
            logger.error(f"Error cleaning unused widgets: {e}")
    
    def _run_garbage_collection(self):
        """Run garbage collection with tracking"""
        try:
            start_time = time.time()
            
            # Get object counts before GC
            before_objects = len(gc.get_objects())
            
            # Run garbage collection
            collected = gc.collect()
            
            # Get object counts after GC
            after_objects = len(gc.get_objects())
            
            gc_time = time.time() - start_time
            
            # Record GC statistics
            gc_stats = {
                'time': time.time(),
                'duration': gc_time,
                'collected': collected,
                'objects_before': before_objects,
                'objects_after': after_objects
            }
            
            self.gc_history.append(gc_stats)
            
            # Keep only recent GC history
            if len(self.gc_history) > 50:
                self.gc_history = self.gc_history[-25:]
            
            logger.debug(f"🗑️ GC collected {collected} objects in {gc_time:.3f}s "
                        f"({before_objects} -> {after_objects} objects)")
            
        except Exception as e:
            logger.error(f"Error running garbage collection: {e}")
    
    def track_object(self, obj, obj_type, estimated_size_mb=1):
        """Track an object for memory management"""
        try:
            obj_ref = weakref.ref(obj)
            self.tracked_objects[obj_type].append(obj_ref)
            self.object_sizes[id(obj)] = estimated_size_mb
            
            # Limit tracking to prevent memory leaks
            if len(self.tracked_objects[obj_type]) > 100:
                # Remove oldest references
                self.tracked_objects[obj_type] = self.tracked_objects[obj_type][-50:]
                
        except Exception as e:
            logger.error(f"Error tracking object: {e}")
    
    def get_memory_stats(self):
        """Get memory statistics"""
        try:
            stats = {
                'current_memory_mb': self.current_memory_mb,
                'target_memory_mb': self.target_memory_mb,
                'usage_percent': (self.current_memory_mb / self.target_memory_mb) * 100,
                'tracked_objects': {k: len(v) for k, v in self.tracked_objects.items()},
                'gc_count': len(self.gc_history),
                'last_gc_time': self.last_gc_time
            }
            
            # Add recent memory trend
            if len(self.memory_history) >= 2:
                recent_memory = [h['memory_mb'] for h in self.memory_history[-5:]]
                stats['memory_trend'] = 'increasing' if recent_memory[-1] > recent_memory[0] else 'stable'
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {}
    
    def force_cleanup(self):
        """Force immediate memory cleanup"""
        try:
            logger.info("🧹 Forcing immediate memory cleanup")
            self._aggressive_cleanup()
            self._run_garbage_collection()
        except Exception as e:
            logger.error(f"Error in forced cleanup: {e}")
    
    def shutdown(self):
        """Shutdown memory optimizer"""
        try:
            self.running = False
            if self.optimization_thread and self.optimization_thread.is_alive():
                self.optimization_thread.join(timeout=5)
            logger.info("Memory optimizer shutdown")
        except Exception as e:
            logger.error(f"Error shutting down memory optimizer: {e}")
