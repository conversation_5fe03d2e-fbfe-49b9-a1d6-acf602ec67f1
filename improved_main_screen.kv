<MusicPlayer>:
    orientation: 'vertical'
    theme_name: "Blue"
    size_hint: 1, 1  # Ensure root layout fills entire screen
    MDNavigationLayout:
        id: nav_layout
        size_hint: 1, 1  # Ensure navigation layout fills entire screen
        ScreenManager:
            id: screen_manager
            size_hint: 1, 2  # Ensure screen manager fills entire screen
            MainScreen:
                name: 'main'
                size_hint: 1, 1  # Ensure main screen fills entire screen
                MDBoxLayout:
                    orientation: 'vertical'
                    size_hint: 1, 1  # Ensure box layout fills entire screen

                    # شريط العنوان العلوي المحسن
                    MDCard:
                        id: top_app_bar_card
                        size_hint_y: None
                        height: dp(70)
                        elevation: dp(10)
                        radius: [0, 0, dp(25), dp(25)]
                        padding: [dp(5), dp(5), dp(5), dp(5)]
                        md_bg_color: root.get_primary_color()
                        shadow_softness: dp(12)
                        shadow_offset: [0, dp(2)]

                        MDBoxLayout:
                            orientation: 'horizontal'
                            padding: [dp(10), dp(0), dp(10), dp(0)]
                            spacing: dp(10)

                            # زر القائمة الجانبية
                            MDIconButton:
                                icon: "menu" if not root.is_favorites_visible else "arrow-left"
                                theme_text_color: "Custom"
                                text_color: root.get_text_color()
                                user_font_size: sp(26)
                                pos_hint: {"center_y": .5}
                                on_release:
                                    nav_drawer.set_state("open") if not root.is_favorites_visible else root.back_to_main()
                                ripple_scale: 1.5
                                size_hint: None, None
                                size: dp(48), dp(48)
                                md_bg_color: [1, 1, 1, 0.1]
                                radius: [dp(24)]

                            # عنوان التطبيق
                            MDLabel:
                                id: top_app_bar
                                text: "Favorites" if root.is_favorites_visible else "Music Player"
                                font_style: "H6"
                                theme_text_color: "Custom"
                                text_color: root.get_text_color()
                                halign: "left"
                                valign: "middle"
                                font_name: 'NotoNaskhArabic-VariableFont_wght'
                                size_hint_x: 1
                                font_size: sp(22)
                                bold: True

                            # أزرار الإجراءات اليمنى
                            MDBoxLayout:
                                orientation: 'horizontal'
                                spacing: dp(10)
                                size_hint_x: None
                                width: self.minimum_width

                                # زر البحث على الإنترنت
                                MDIconButton:
                                    icon: "web"
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    user_font_size: sp(24)
                                    pos_hint: {"center_y": .5}
                                    on_release: root.show_search_screen()
                                    ripple_scale: 1.5
                                    size_hint: None, None
                                    size: dp(48), dp(48)
                                    md_bg_color: [1, 1, 1, 0.1]
                                    radius: [dp(24)]

                                # زر البحث المحلي
                                MDIconButton:
                                    icon: "search" if root.is_search_visible else "magnify"
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    user_font_size: sp(24)
                                    pos_hint: {"center_y": .5}
                                    on_release: root.perform_search() if root.is_search_visible else root.toggle_search()
                                    ripple_scale: 1.5
                                    size_hint: None, None
                                    size: dp(48), dp(48)
                                    md_bg_color: [1, 1, 1, 0.1]
                                    radius: [dp(24)]

                                # زر إغلاق البحث (يظهر فقط عند تفعيل البحث)
                                MDIconButton:
                                    icon: "close"
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    user_font_size: sp(24)
                                    pos_hint: {"center_y": .5}
                                    opacity: 1 if root.is_search_visible else 0
                                    disabled: not root.is_search_visible
                                    on_release: root.toggle_search()
                                    ripple_scale: 1.5
                                    size_hint: None, None
                                    size: dp(48), dp(48)
                                    md_bg_color: [1, 1, 1, 0.1] if root.is_search_visible else [0, 0, 0, 0]
                                    radius: [dp(24)]

                    # حقل البحث المحسن والمتقدم
                    MDCard:
                        size_hint_y: None
                        height: dp(120) if root.is_search_visible else 0
                        padding: [dp(15), dp(10), dp(15), dp(10)]
                        radius: [dp(20)]
                        elevation: dp(8) if root.is_search_visible else 0
                        opacity: 1 if root.is_search_visible else 0
                        md_bg_color: [0.98, 0.98, 0.98, 1] if app.theme_cls.theme_style == "Light" else [0.15, 0.15, 0.15, 1]
                        shadow_softness: dp(12) if root.is_search_visible else 0
                        shadow_offset: [0, dp(3)]

                        MDBoxLayout:
                            orientation: 'vertical'
                            spacing: dp(8)

                            # الصف الأول - حقل البحث الرئيسي
                            MDBoxLayout:
                                orientation: 'horizontal'
                                spacing: dp(8)
                                size_hint_y: None
                                height: dp(50)

                                # أيقونة البحث
                                MDIconButton:
                                    icon: "magnify"
                                    theme_text_color: "Custom"
                                    text_color: root.get_primary_color()
                                    user_font_size: sp(26)
                                    pos_hint: {"center_y": .5}
                                    size_hint: None, None
                                    size: dp(45), dp(45)
                                    md_bg_color: root.get_primary_color()[:3] + [0.1]
                                    radius: [dp(22)]

                                # حقل البحث المحسن
                                MDTextField:
                                    id: search_field
                                    hint_text: "🔍 ابحث عن الأغاني، الفنانين، الألبومات..." if app.root.contains_arabic("ابحث") else "🔍 Search songs, artists, albums..."
                                    helper_text: "البحث الذكي مع الاقتراحات" if app.root.contains_arabic("البحث") else "Smart search with suggestions"
                                    helper_text_mode: "on_focus"
                                    size_hint_x: 1
                                    pos_hint: {'center_y': 0.5}
                                    disabled: not root.is_search_visible
                                    on_text:
                                        root.enhanced_search_tracks(self.text)
                                        self.halign = 'right' if app.root.contains_arabic(self.text) else 'left'
                                    on_focus:
                                        root.toggle_search_suggestions(self.focus)
                                    line_color_focus: root.get_primary_color()
                                    line_color_normal: [0, 0, 0, 0]
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    text_color: [0.1, 0.1, 0.1, 1] if app.theme_cls.theme_style == "Light" else [0.95, 0.95, 0.95, 1]
                                    hint_text_color: [0.6, 0.6, 0.6, 1]
                                    multiline: False
                                    halign: 'right' if app.root.contains_arabic(hint_text) else 'left'
                                    hint_text_color_focus: root.get_primary_color()
                                    cursor_color: root.get_primary_color()
                                    cursor_width: dp(3)
                                    font_size: sp(16)
                                    background_color: [0, 0, 0, 0]

                                # زر المسح
                                MDIconButton:
                                    icon: "close-circle"
                                    theme_text_color: "Custom"
                                    text_color: [0.6, 0.6, 0.6, 1]
                                    user_font_size: sp(22)
                                    pos_hint: {"center_y": .5}
                                    size_hint: None, None
                                    size: dp(40), dp(40)
                                    opacity: 1 if search_field.text else 0
                                    disabled: not bool(search_field.text)
                                    on_release:
                                        search_field.text = ""
                                        root.enhanced_search_tracks("")
                                        root.hide_search_suggestions()

                                # زر الفلاتر المتقدمة
                                MDIconButton:
                                    icon: "filter-variant"
                                    theme_text_color: "Custom"
                                    text_color: root.get_primary_color() if root.search_filters_active else [0.6, 0.6, 0.6, 1]
                                    user_font_size: sp(22)
                                    pos_hint: {"center_y": .5}
                                    size_hint: None, None
                                    size: dp(40), dp(40)
                                    on_release: root.toggle_search_filters()

                            # الصف الثاني - أزرار البحث السريع والإحصائيات
                            MDBoxLayout:
                                orientation: 'horizontal'
                                spacing: dp(8)
                                size_hint_y: None
                                height: dp(35)
                                opacity: 1 if root.is_search_visible else 0

                                # أزرار البحث السريع
                                MDChip:
                                    text: "🎵 الكل"
                                    size_hint: None, None
                                    height: dp(32)
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    on_release: root.quick_search_filter("all")
                                    md_bg_color: root.get_primary_color() if root.current_search_filter == "all" else [0.8, 0.8, 0.8, 0.3]

                                MDChip:
                                    text: "🎤 أصوات"
                                    size_hint: None, None
                                    height: dp(32)
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    on_release: root.quick_search_filter("vocal")
                                    md_bg_color: root.get_primary_color() if root.current_search_filter == "vocal" else [0.8, 0.8, 0.8, 0.3]

                                MDChip:
                                    text: "🎸 آلات"
                                    size_hint: None, None
                                    height: dp(32)
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    on_release: root.quick_search_filter("instrumental")
                                    md_bg_color: root.get_primary_color() if root.current_search_filter == "instrumental" else [0.8, 0.8, 0.8, 0.3]

                                MDChip:
                                    text: "⭐ مفضلة"
                                    size_hint: None, None
                                    height: dp(32)
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    on_release: root.quick_search_filter("favorites")
                                    md_bg_color: root.get_primary_color() if root.current_search_filter == "favorites" else [0.8, 0.8, 0.8, 0.3]

                                # مساحة فارغة
                                Widget:

                                # عداد النتائج
                                MDLabel:
                                    id: search_results_count
                                    text: f"{len(root.playlist)} أغنية" if app.root.contains_arabic("أغنية") else f"{len(root.playlist)} songs"
                                    font_size: sp(12)
                                    theme_text_color: "Secondary"
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    size_hint: None, None
                                    width: self.texture_size[0]
                                    height: dp(32)
                                    halign: "center"
                                    valign: "center"

                    # قائمة الاقتراحات
                    MDCard:
                        id: suggestions_card
                        size_hint_y: None
                        height: dp(150) if root.show_search_suggestions else 0
                        padding: [dp(15), dp(8), dp(15), dp(8)]
                        radius: [dp(15)]
                        elevation: dp(4) if root.show_search_suggestions else 0
                        opacity: 1 if root.show_search_suggestions else 0
                        md_bg_color: [0.95, 0.95, 0.95, 1] if app.theme_cls.theme_style == "Light" else [0.2, 0.2, 0.2, 1]

                        MDScrollView:
                            MDBoxLayout:
                                id: suggestions_list
                                orientation: 'vertical'
                                size_hint_y: None
                                height: self.minimum_height
                                spacing: dp(4)

                    # قائمة الأغاني المحسنة
                    ScrollView:
                        size_hint: 1, 1  # Fill remaining space
                        do_scroll_x: False
                        effect_cls: "ScrollEffect"  # Smooth scrolling effect
                        bar_width: dp(4)
                        bar_color: root.get_primary_color()
                        bar_inactive_color: [0.7, 0.7, 0.7, 0.5]

                        MDGridLayout:
                            id: playlist_list
                            cols: 1
                            size_hint_y: None
                            height: self.minimum_height
                            padding: dp(15)
                            spacing: dp(12)
                            adaptive_height: True
                            canvas.before:
                                Color:
                                    rgba: root.get_bg_color()
                                Rectangle:
                                    pos: self.pos
                                    size: self.size

                    # شريط التحكم السفلي المحسن
                    MDCard:
                        id: bottom_bar
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(90)
                        padding: [dp(15), dp(10), dp(15), dp(10)]
                        spacing: dp(5)
                        elevation: dp(18)
                        radius: [dp(30), dp(30), 0, 0]
                        md_bg_color: root.get_primary_color()
                        shadow_offset: [0, -dp(4)]
                        shadow_softness: dp(20)
                        shadow_color: [0, 0, 0, 0.5]
                        on_release: root.open_now_playing_on_click() if root.is_playing or root.is_online_song else None

                        # Main content layout
                        BoxLayout:
                            orientation: 'horizontal'
                            spacing: dp(12)

                            # Album cover with rounded corners
                            MDCard:
                                size_hint: None, None
                                size: dp(60), dp(60)
                                radius: [dp(15)]
                                elevation: dp(5)
                                padding: 0
                                pos_hint: {'center_y': 0.5}
                                md_bg_color: [0.9, 0.9, 0.9, 1]
                                shadow_softness: dp(8)
                                shadow_offset: [0, dp(2)]

                                AsyncImage:
                                    id: bottom_bar_album_cover
                                    source: ""
                                    allow_stretch: True
                                    keep_ratio: True
                                    opacity: 0.95  # Slight transparency for better look

                            # Song name and time (middle)
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_x: 1
                                spacing: dp(4)
                                pos_hint: {'center_y': 0.5}

                                # Song name with animation
                                MDLabel:
                                    id: current_track_name
                                    text: 'No Track Playing'
                                    halign: 'left'
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    font_style: 'Subtitle1'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    bold: True
                                    shorten: True
                                    shorten_from: 'right'

                                # Time display with progress bar
                                BoxLayout:
                                    orientation: 'vertical'
                                    size_hint_y: None
                                    height: dp(25)
                                    spacing: dp(2)

                                    # Time labels
                                    BoxLayout:
                                        orientation: 'horizontal'
                                        size_hint_y: None
                                        height: dp(15)

                                        MDLabel:
                                            id: current_time_main
                                            text: '00:00'
                                            theme_text_color: "Custom"
                                            text_color: root.get_text_color()
                                            font_size: sp(12)
                                            size_hint_x: None
                                            width: dp(40)
                                            halign: 'left'

                                        Widget:
                                            size_hint_x: 1

                                        MDLabel:
                                            id: total_time_main
                                            text: '00:00'
                                            theme_text_color: "Custom"
                                            text_color: root.get_text_color()
                                            font_size: sp(12)
                                            size_hint_x: None
                                            width: dp(40)
                                            halign: 'right'

                                    # Modern progress bar
                                    BoxLayout:
                                        size_hint_y: None
                                        height: dp(6)
                                        padding: 0

                                        canvas:
                                            Color:
                                                rgba: [1, 1, 1, 0.3]
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.width, dp(4)
                                                radius: [dp(2)]

                                            Color:
                                                rgba: [1, 1, 1, 0.9]
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.width * (root.current_pos / root.get_track_length()) if root.get_track_length() > 0 else 0, dp(4)
                                                radius: [dp(2)]

                            # Play button with circular progress
                            RelativeLayout:
                                id: play_button_container
                                size_hint: None, None
                                size: dp(60), dp(60)
                                pos_hint: {'center_y': 0.5}

                                # Circular progress bar
                                CircularProgressBar:
                                    id: play_progress_main
                                    size_hint: None, None
                                    size: dp(60), dp(60)
                                    pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                                    value: root.current_pos
                                    max: root.get_track_length() if root.get_track_length() > 0 else 100
                                    thickness: dp(4)
                                    color: [1, 1, 1, 0.95]

                                # Play/Pause button
                                MDIconButton:
                                    icon: 'play' if not root.is_playing else 'pause'
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    on_release: root.toggle_play_only()
                                    md_bg_color: [1, 1, 1, 0.2]
                                    size_hint: None, None
                                    size: dp(52), dp(52)
                                    radius: [dp(26)]
                                    pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                                    ripple_scale: 1.2
                                    user_font_size: sp(28)
