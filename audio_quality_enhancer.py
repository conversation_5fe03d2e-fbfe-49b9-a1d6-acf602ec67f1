"""
محسن جودة الصوت التلقائي - Automatic Audio Quality Enhancer
يحلل الملفات الصوتية ويطبق التحسينات المناسبة تلقائياً للحصول على أفضل جودة صوت
"""

import os
import logging
import numpy as np
import threading
import time
from collections import defaultdict

logger = logging.getLogger(__name__)

class AudioQualityEnhancer:
    """محسن جودة الصوت التلقائي"""
    
    def __init__(self, advanced_filters=None):
        self.advanced_filters = advanced_filters
        
        # قاعدة بيانات تحليل الصوت
        self.audio_analysis_cache = {}
        
        # إعدادات التحليل التلقائي
        self.auto_analysis_enabled = True
        self.smart_enhancement_enabled = True
        
        # قواعد التحسين التلقائي
        self.enhancement_rules = {
            'vocal_heavy': {
                'equalizer': {'mid': 3, 'high_mid': 4, 'presence': 5},
                'enhancement': {'vocal_enhancement': True, 'noise_reduction': 0.2}
            },
            'bass_heavy': {
                'equalizer': {'sub_bass': 6, 'bass': 4},
                'enhancement': {'bass_boost': 0.3, 'dynamic_range_compression': 0.4}
            },
            'classical': {
                'equalizer': {'high_mid': 1, 'presence': 2, 'brilliance': 3},
                'enhancement': {'spatial_enhancement': 0.3, 'dynamic_range_compression': 0.1}
            },
            'electronic': {
                'equalizer': {'sub_bass': 8, 'bass': 5, 'brilliance': 6},
                'enhancement': {'stereo_enhancement': 0.4, 'exciter': 0.3}
            },
            'low_quality': {
                'equalizer': {'presence': 3, 'brilliance': 4},
                'enhancement': {'noise_reduction': 0.3, 'harmonic_enhancement': 0.2}
            },
            'compressed': {
                'equalizer': {'bass': 2, 'mid': 1, 'presence': 2},
                'enhancement': {'dynamic_range_compression': 0.2, 'maximizer': 0.3}
            }
        }
        
        # إحصائيات التحسين
        self.enhancement_stats = {
            'files_analyzed': 0,
            'auto_enhancements_applied': 0,
            'quality_improvements': 0,
            'processing_time_total': 0
        }
        
        logger.info("🎯 Audio Quality Enhancer initialized")

    def analyze_audio_characteristics(self, audio_path):
        """تحليل خصائص الملف الصوتي"""
        try:
            # فحص ذاكرة التخزين المؤقت أولاً
            if audio_path in self.audio_analysis_cache:
                return self.audio_analysis_cache[audio_path]
            
            start_time = time.time()
            
            # تحليل أساسي من اسم الملف ومعلوماته
            analysis = self._analyze_basic_characteristics(audio_path)
            
            # تحليل متقدم إذا كان متاحاً
            if self._can_perform_advanced_analysis():
                advanced_analysis = self._analyze_advanced_characteristics(audio_path)
                analysis.update(advanced_analysis)
            
            # حفظ في ذاكرة التخزين المؤقت
            self.audio_analysis_cache[audio_path] = analysis
            
            # تحديث الإحصائيات
            self.enhancement_stats['files_analyzed'] += 1
            self.enhancement_stats['processing_time_total'] += time.time() - start_time
            
            logger.debug(f"🔍 Audio analysis completed: {os.path.basename(audio_path)}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing audio: {e}")
            return self._get_default_analysis()

    def _analyze_basic_characteristics(self, audio_path):
        """تحليل أساسي للخصائص"""
        try:
            analysis = {
                'file_size': 0,
                'estimated_bitrate': 'unknown',
                'estimated_quality': 'medium',
                'genre_hint': 'unknown',
                'vocal_content': 'unknown',
                'dynamic_range': 'medium'
            }
            
            # تحليل حجم الملف
            if os.path.exists(audio_path):
                file_size = os.path.getsize(audio_path)
                analysis['file_size'] = file_size
                
                # تقدير جودة الملف من الحجم (تقريبي)
                filename = os.path.basename(audio_path).lower()
                duration_estimate = 180  # افتراض 3 دقائق
                
                if file_size < 2 * 1024 * 1024:  # أقل من 2MB
                    analysis['estimated_quality'] = 'low'
                    analysis['estimated_bitrate'] = '128k'
                elif file_size < 5 * 1024 * 1024:  # أقل من 5MB
                    analysis['estimated_quality'] = 'medium'
                    analysis['estimated_bitrate'] = '192k'
                else:  # أكبر من 5MB
                    analysis['estimated_quality'] = 'high'
                    analysis['estimated_bitrate'] = '320k'
            
            # تحليل النوع من اسم الملف
            filename_lower = os.path.basename(audio_path).lower()
            
            # كلمات مفتاحية للأنواع الموسيقية
            genre_keywords = {
                'rock': ['rock', 'metal', 'punk', 'grunge'],
                'pop': ['pop', 'dance', 'hit', 'chart'],
                'classical': ['classical', 'symphony', 'concerto', 'opera', 'bach', 'mozart'],
                'jazz': ['jazz', 'blues', 'swing', 'bebop'],
                'electronic': ['electronic', 'techno', 'house', 'edm', 'dubstep', 'trance'],
                'vocal': ['vocal', 'singer', 'voice', 'acapella', 'choir']
            }
            
            for genre, keywords in genre_keywords.items():
                if any(keyword in filename_lower for keyword in keywords):
                    analysis['genre_hint'] = genre
                    break
            
            # تحديد محتوى الصوت
            vocal_keywords = ['vocal', 'singer', 'voice', 'song', 'lyrics']
            if any(keyword in filename_lower for keyword in vocal_keywords):
                analysis['vocal_content'] = 'high'
            elif 'instrumental' in filename_lower:
                analysis['vocal_content'] = 'low'
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in basic analysis: {e}")
            return self._get_default_analysis()

    def _analyze_advanced_characteristics(self, audio_path):
        """تحليل متقدم للخصائص (يتطلب مكتبات إضافية)"""
        try:
            # هذا التحليل يتطلب مكتبات مثل librosa أو scipy
            # للآن سنستخدم تحليل مبسط
            
            advanced_analysis = {
                'frequency_profile': 'balanced',
                'dynamic_range_score': 0.5,
                'noise_level': 'low',
                'stereo_width': 'medium',
                'peak_frequency': 1000  # Hz
            }
            
            # يمكن إضافة تحليل أكثر تعقيداً هنا
            
            return advanced_analysis
            
        except Exception as e:
            logger.debug(f"Advanced analysis not available: {e}")
            return {}

    def _can_perform_advanced_analysis(self):
        """فحص إمكانية التحليل المتقدم"""
        try:
            # فحص توفر المكتبات المطلوبة
            import numpy
            return True
        except ImportError:
            return False

    def _get_default_analysis(self):
        """تحليل افتراضي"""
        return {
            'estimated_quality': 'medium',
            'genre_hint': 'unknown',
            'vocal_content': 'unknown',
            'dynamic_range': 'medium'
        }

    def get_optimal_settings(self, audio_path):
        """الحصول على الإعدادات المثلى للملف الصوتي"""
        try:
            if not self.smart_enhancement_enabled:
                return None
            
            # تحليل الملف
            analysis = self.analyze_audio_characteristics(audio_path)
            
            # تحديد نوع التحسين المطلوب
            enhancement_type = self._determine_enhancement_type(analysis)
            
            # الحصول على الإعدادات المناسبة
            if enhancement_type in self.enhancement_rules:
                settings = self.enhancement_rules[enhancement_type].copy()
                
                # تخصيص الإعدادات حسب التحليل
                settings = self._customize_settings(settings, analysis)
                
                logger.info(f"🎯 Optimal settings determined: {enhancement_type} for {os.path.basename(audio_path)}")
                return settings
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting optimal settings: {e}")
            return None

    def _determine_enhancement_type(self, analysis):
        """تحديد نوع التحسين المطلوب"""
        try:
            # قواعد تحديد نوع التحسين
            
            # فحص الجودة أولاً
            if analysis.get('estimated_quality') == 'low':
                return 'low_quality'
            
            # فحص النوع الموسيقي
            genre = analysis.get('genre_hint', 'unknown')
            if genre in ['rock', 'metal']:
                return 'bass_heavy'
            elif genre == 'classical':
                return 'classical'
            elif genre == 'electronic':
                return 'electronic'
            elif genre == 'vocal' or analysis.get('vocal_content') == 'high':
                return 'vocal_heavy'
            
            # فحص خصائص أخرى
            if analysis.get('estimated_bitrate') in ['128k', '160k']:
                return 'compressed'
            
            # افتراضي
            return 'compressed'  # تحسين عام للملفات المضغوطة
            
        except Exception as e:
            logger.error(f"Error determining enhancement type: {e}")
            return 'compressed'

    def _customize_settings(self, settings, analysis):
        """تخصيص الإعدادات حسب التحليل"""
        try:
            customized = settings.copy()
            
            # تعديل حسب جودة الملف
            quality = analysis.get('estimated_quality', 'medium')
            if quality == 'low':
                # زيادة تقليل الضوضاء للملفات منخفضة الجودة
                if 'enhancement' in customized:
                    customized['enhancement']['noise_reduction'] = min(0.4, 
                        customized['enhancement'].get('noise_reduction', 0) + 0.1)
            
            # تعديل حسب حجم الملف
            file_size = analysis.get('file_size', 0)
            if file_size > 0 and file_size < 3 * 1024 * 1024:  # ملفات صغيرة
                # تحسين إضافي للملفات المضغوطة
                if 'enhancement' in customized:
                    customized['enhancement']['harmonic_enhancement'] = 0.15
            
            return customized
            
        except Exception as e:
            logger.error(f"Error customizing settings: {e}")
            return settings

    def apply_automatic_enhancement(self, audio_path):
        """تطبيق التحسين التلقائي"""
        try:
            if not self.advanced_filters:
                logger.warning("Advanced filters not available for automatic enhancement")
                return False
            
            # الحصول على الإعدادات المثلى
            optimal_settings = self.get_optimal_settings(audio_path)
            
            if not optimal_settings:
                logger.debug(f"No automatic enhancement needed for {os.path.basename(audio_path)}")
                return False
            
            # تطبيق إعدادات الإيكولايزر
            if 'equalizer' in optimal_settings:
                self.advanced_filters.set_equalizer_advanced(**optimal_settings['equalizer'])
            
            # تطبيق فلاتر التحسين
            if 'enhancement' in optimal_settings:
                self.advanced_filters.set_enhancement_filters(**optimal_settings['enhancement'])
            
            # تحديث الإحصائيات
            self.enhancement_stats['auto_enhancements_applied'] += 1
            
            logger.info(f"✨ Automatic enhancement applied to {os.path.basename(audio_path)}")
            return True
            
        except Exception as e:
            logger.error(f"Error applying automatic enhancement: {e}")
            return False

    def analyze_and_enhance(self, audio_path):
        """تحليل وتحسين تلقائي شامل"""
        try:
            start_time = time.time()
            
            # تحليل الملف
            analysis = self.analyze_audio_characteristics(audio_path)
            
            # تطبيق التحسين التلقائي
            enhancement_applied = self.apply_automatic_enhancement(audio_path)
            
            # تسجيل النتائج
            processing_time = time.time() - start_time
            
            result = {
                'analysis': analysis,
                'enhancement_applied': enhancement_applied,
                'processing_time': processing_time,
                'recommendations': self._generate_recommendations(analysis)
            }
            
            if enhancement_applied:
                self.enhancement_stats['quality_improvements'] += 1
            
            logger.info(f"🎯 Analysis and enhancement completed in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error in analyze and enhance: {e}")
            return None

    def _generate_recommendations(self, analysis):
        """توليد توصيات للمستخدم"""
        try:
            recommendations = []
            
            quality = analysis.get('estimated_quality', 'medium')
            if quality == 'low':
                recommendations.append("Consider using noise reduction filter")
                recommendations.append("Apply harmonic enhancement for better clarity")
            
            genre = analysis.get('genre_hint', 'unknown')
            if genre == 'vocal':
                recommendations.append("Enable vocal enhancement filter")
            elif genre == 'electronic':
                recommendations.append("Try bass boost for better impact")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []

    def get_enhancement_report(self):
        """الحصول على تقرير التحسين"""
        try:
            avg_processing_time = 0
            if self.enhancement_stats['files_analyzed'] > 0:
                avg_processing_time = (self.enhancement_stats['processing_time_total'] / 
                                     self.enhancement_stats['files_analyzed'])
            
            return {
                'stats': self.enhancement_stats.copy(),
                'average_processing_time': avg_processing_time,
                'cache_size': len(self.audio_analysis_cache),
                'auto_analysis_enabled': self.auto_analysis_enabled,
                'smart_enhancement_enabled': self.smart_enhancement_enabled
            }
            
        except Exception as e:
            logger.error(f"Error generating enhancement report: {e}")
            return {}

    def clear_analysis_cache(self):
        """مسح ذاكرة التخزين المؤقت للتحليل"""
        try:
            self.audio_analysis_cache.clear()
            logger.info("🧹 Audio analysis cache cleared")
        except Exception as e:
            logger.error(f"Error clearing analysis cache: {e}")

    def set_auto_analysis(self, enabled):
        """تفعيل أو إلغاء التحليل التلقائي"""
        self.auto_analysis_enabled = enabled
        logger.info(f"🔍 Auto analysis: {'enabled' if enabled else 'disabled'}")

    def set_smart_enhancement(self, enabled):
        """تفعيل أو إلغاء التحسين الذكي"""
        self.smart_enhancement_enabled = enabled
        logger.info(f"🎯 Smart enhancement: {'enabled' if enabled else 'disabled'}")

# دالة مساعدة لتطبيق النظام
def apply_audio_quality_enhancer(app, advanced_filters=None):
    """تطبيق محسن جودة الصوت على التطبيق"""
    try:
        if not hasattr(app, 'audio_quality_enhancer'):
            app.audio_quality_enhancer = AudioQualityEnhancer(advanced_filters)
            logger.info("🎯 Audio Quality Enhancer applied to app")
        return app.audio_quality_enhancer
    except Exception as e:
        logger.error(f"Failed to apply audio quality enhancer: {e}")
        return None
