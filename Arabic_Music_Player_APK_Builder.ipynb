{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎵 Arabic Music Player - APK Builder\n", "## تحويل تطبيق مشغل الموسيقى العربي إلى APK\n", "\n", "هذا الدفتر يحتوي على جميع الخطوات المطلوبة لتحويل التطبيق إلى APK باستخدام Google Colab.\n", "\n", "### المتطلبات:\n", "- ملف ZIP للتطبيق: `ArabicMusicPlayer_APK_20250603_201701.zip`\n", "- اتصال إنترنت مستقر\n", "- صب<PERSON> (العملية تستغرق 30-60 دقيقة)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## الخطوة 1: رفع ملف ZIP"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# رفع ملف ZIP\n", "from google.colab import files\n", "import zipfile\n", "import os\n", "\n", "print(\"📁 ارفع ملف ZIP للتطبيق...\")\n", "uploaded = files.upload()\n", "\n", "# العثور على ملف ZIP\n", "zip_file = None\n", "for filename in uploaded.keys():\n", "    if filename.endswith('.zip'):\n", "        zip_file = filename\n", "        break\n", "\n", "if zip_file:\n", "    print(f\"✅ تم رفع الملف: {zip_file}\")\n", "else:\n", "    print(\"❌ لم يتم العثور على ملف ZIP\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## الخطوة 2: فك ضغط الملف"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# فك ضغط الملف\n", "if zip_file:\n", "    print(\"📦 فك ضغط الملف...\")\n", "    \n", "    # إنشاء مجلد للمشروع\n", "    project_dir = '/content/arabic_music_player'\n", "    os.makedirs(project_dir, exist_ok=True)\n", "    \n", "    # فك الضغط\n", "    with zipfile.ZipFile(zip_file, 'r') as zip_ref:\n", "        zip_ref.extractall(project_dir)\n", "    \n", "    # الانتقال إلى مجلد المشروع\n", "    os.chdir(project_dir)\n", "    \n", "    print(\"✅ تم فك ضغط الملف بنجاح\")\n", "    print(\"📋 محتويات المجلد:\")\n", "    !ls -la\n", "else:\n", "    print(\"❌ لا يوجد ملف ZIP لفك ضغطه\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## الخطوة 3: تثبيت الأدوات المطلوبة"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# تحديث النظام وتثبيت Java\n", "print(\"🔧 تثبيت الأدوات الأساسية...\")\n", "\n", "!apt update -qq\n", "!apt install -y openjdk-8-jdk\n", "\n", "# تعيين متغير JAVA_HOME\n", "import os\n", "os.environ['JAVA_HOME'] = '/usr/lib/jvm/java-8-openjdk-amd64'\n", "\n", "print(\"✅ تم تثبيت Java 8\")\n", "!java -version"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# تثبيت Python 3.11 والمكتبات\n", "print(\"🐍 تثبيت Python 3.11 والمكتبات...\")\n", "\n", "!apt install -y python3.11 python3.11-dev python3.11-venv\n", "!curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11\n", "\n", "# تثبيت buildozer والمكتبات المطلوبة\n", "!python3.11 -m pip install buildozer cython\n", "!python3.11 -m pip install kivy k<PERSON><PERSON><PERSON> ply<PERSON> requests mutagen arabic-reshaper python-bidi yt-dlp pygame pillow urllib3 certifi\n", "\n", "print(\"✅ تم تثبيت Python 3.11 والمكتبات\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## الخطوة 4: إعد<PERSON> Android SDK و NDK"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# تحضير Android SDK\n", "print(\"📱 تحضير Android SDK...\")\n", "\n", "# إنشاء مجلد للأدوات\n", "!mkdir -p /content/android-tools\n", "os.chdir('/content/android-tools')\n", "\n", "# تحميل Android SDK Command Line Tools\n", "!wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip\n", "!unzip -q commandlinetools-linux-9477386_latest.zip\n", "\n", "# إعداد متغيرات البيئة\n", "os.environ['ANDROID_HOME'] = '/content/android-tools'\n", "os.environ['ANDROID_SDK_ROOT'] = '/content/android-tools'\n", "os.environ['PATH'] = f\"{os.environ['PATH']}:/content/android-tools/cmdline-tools/bin\"\n", "\n", "print(\"✅ تم تحضير Android SDK\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# تثبيت Android NDK والأدوات\n", "print(\"🛠️ تثبيت Android NDK 25...\")\n", "\n", "# تثبيت الأدوات المطلوبة\n", "!yes | /content/android-tools/cmdline-tools/bin/sdkmanager --sdk_root=/content/android-tools \"ndk;25.2.9519653\"\n", "!yes | /content/android-tools/cmdline-tools/bin/sdkmanager --sdk_root=/content/android-tools \"platforms;android-33\"\n", "!yes | /content/android-tools/cmdline-tools/bin/sdkmanager --sdk_root=/content/android-tools \"build-tools;33.0.2\"\n", "\n", "# تعيين متغير NDK\n", "os.environ['ANDROID_NDK_HOME'] = '/content/android-tools/ndk/25.2.9519653'\n", "\n", "print(\"✅ تم تثبيت Android NDK 25\")\n", "print(f\"NDK Path: {os.environ.get('ANDROID_NDK_HOME')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## الخطوة 5: تحضير ملف buildozer.spec"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# العودة إلى مجلد المشروع\n", "os.chdir('/content/arabic_music_player')\n", "\n", "# تحديث ملف buildozer.spec\n", "print(\"⚙️ تحضير ملف buildozer.spec...\")\n", "\n", "buildozer_content = '''[app]\n", "title = Arabic Music Player\n", "package.name = arabicmusicplayer\n", "package.domain = com.arabicplayer\n", "\n", "source.dir = .\n", "source.include_exts = py,png,jpg,kv,atlas,json,ttf,wav,mp3\n", "\n", "version = 1.0\n", "requirements = python3,kivy,kivymd,plyer,requests,mutagen,arabic-reshaper,python-bidi,yt-dlp,pygame,pillow,urllib3,certifi\n", "\n", "[buildozer]\n", "log_level = 2\n", "warn_on_root = 0\n", "\n", "[android]\n", "ndk_api = 21\n", "api = 33\n", "minapi = 21\n", "ndk = 25.2.9519653\n", "accept_sdk_license = True\n", "\n", "permissions = INTERNET,READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,FOREGROUND_SERVICE,WAKE_LOCK\n", "\n", "[android.gradle_dependencies]\n", "implementation 'androidx.core:core:1.6.0'\n", "implementation 'androidx.appcompat:appcompat:1.3.1'\n", "'''\n", "\n", "with open('buildozer.spec', 'w', encoding='utf-8') as f:\n", "    f.write(buildozer_content)\n", "\n", "print(\"✅ تم تحضير ملف buildozer.spec\")\n", "print(\"📄 محتوى الملف:\")\n", "!head -20 buildozer.spec"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## الخطوة 6: بناء APK"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# بناء APK\n", "print(\"🏗️ بدء بناء APK...\")\n", "print(\"⏰ هذه العملية قد تستغرق 30-60 دقيقة\")\n", "print(\"☕ اذهب واشرب قهوة!\")\n", "\n", "# تشغيل buildozer\n", "!python3.11 -m buildozer android debug"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## الخطوة 7: تحميل APK"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# البحث عن ملف APK وتحميله\n", "import glob\n", "from google.colab import files\n", "\n", "print(\"🔍 البحث عن ملف APK...\")\n", "\n", "# البحث في مج<PERSON><PERSON> bin\n", "apk_files = glob.glob('bin/*.apk')\n", "\n", "if apk_files:\n", "    apk_file = apk_files[0]\n", "    file_size = os.path.getsize(apk_file) / (1024 * 1024)  # بالميجابايت\n", "    \n", "    print(f\"✅ تم العثور على ملف APK: {apk_file}\")\n", "    print(f\"📊 حجم الملف: {file_size:.2f} MB\")\n", "    \n", "    # تحميل الملف\n", "    print(\"📥 تحميل ملف APK...\")\n", "    files.download(apk_file)\n", "    \n", "    print(\"🎉 تم تحميل ملف APK بنجاح!\")\n", "    print(\"📱 يمكنك الآن تثبيت التطبيق على هاتف Android\")\n", "    \n", "else:\n", "    print(\"❌ لم يتم العثور على ملف APK\")\n", "    print(\"🔍 تحقق من رسائل الخطأ في الخطوة السابقة\")\n", "    \n", "    # عرض محتويا<PERSON> مج<PERSON><PERSON> bin\n", "    print(\"📁 محتويات مجلد bin:\")\n", "    !ls -la bin/ 2>/dev/null || echo \"مجلد bin غير موجود\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 حل المشاكل (إذا فشل البناء)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# تنظيف وإعادة المحاولة\n", "print(\"🧹 تنظيف الملفات المؤقتة وإعادة المحاولة...\")\n", "\n", "!python3.11 -m buildozer android clean\n", "!python3.11 -m buildozer android debug"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# حل بديل لمشاكل NDK\n", "print(\"🛠️ حل بديل لمشاكل NDK...\")\n", "\n", "# تحميل NDK بطريقة مختلفة\n", "!wget -q https://dl.google.com/android/repository/android-ndk-r25c-linux.zip -O /content/ndk.zip\n", "!unzip -q /content/ndk.zip -d /content/android-tools/\n", "!mv /content/android-tools/android-ndk-r25c /content/android-tools/ndk/25.2.9519653\n", "\n", "# إضافة أذونات\n", "!chmod +x /content/android-tools/cmdline-tools/bin/*\n", "!chmod +x /content/android-tools/ndk/25.2.9519653/toolchains/llvm/prebuilt/linux-x86_64/bin/*\n", "\n", "print(\"✅ تم تطبيق الحلول البديلة\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 ملخص العملية\n", "\n", "### ما تم إنجازه:\n", "1. ✅ رفع ملف ZIP للتطبيق\n", "2. ✅ فك ضغط الملفات\n", "3. ✅ تثبيت Java 8 و Python 3.11\n", "4. ✅ تثبيت buildozer والمكتبات\n", "5. ✅ إعداد Android SDK و NDK\n", "6. ✅ تحضير ملف buildozer.spec\n", "7. ✅ بناء APK\n", "8. ✅ تحميل APK\n", "\n", "### النتيجة:\n", "- ملف <PERSON>K جاهز للتثبيت على أجهزة Android\n", "- دعم كامل للنصوص العربية\n", "- جميع وظائف التطبيق متاحة\n", "\n", "### خطوات التثبيت على الهاتف:\n", "1. انقل ملف APK إلى هاتف Android\n", "2. فعّل \"مصادر غير معروفة\" في الإعدادات\n", "3. ا<PERSON><PERSON><PERSON> على ملف APK لتثبيت التطبيق\n", "4. استمتع بتطبيق مشغل الموسيقى العربي!\n", "\n", "---\n", "**تم إنشاء هذا الدفتر بواسطة Augment Agent** 🤖"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}