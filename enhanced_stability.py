"""
نظام الاستقرار المحسن - Enhanced Stability System
يوفر تحسينات شاملة للاستقرار والأداء والسرعة
"""

import time
import threading
import gc
import logging
import os
import sys
from functools import wraps
from kivy.clock import Clock
from kivy.cache import Cache

logger = logging.getLogger(__name__)

class EnhancedStabilitySystem:
    """نظام الاستقرار المحسن للتطبيق"""
    
    def __init__(self, app):
        self.app = app
        self.is_active = False
        self.performance_mode = 'auto'
        
        # إحصائيات الأداء
        self.stats = {
            'errors_count': 0,
            'crashes_prevented': 0,
            'memory_cleanups': 0,
            'performance_optimizations': 0,
            'startup_time': 0,
            'average_response_time': 0
        }
        
        # إعدادات التحسين
        self.settings = {
            'auto_recovery': True,
            'memory_monitoring': True,
            'performance_optimization': True,
            'error_prevention': True,
            'smart_caching': True
        }
        
        self._initialize_system()
        logger.info("🚀 Enhanced Stability System initialized")

    def _initialize_system(self):
        """تهيئة النظام"""
        try:
            # تطبيق تحسينات الذاكرة
            self._optimize_memory_settings()
            
            # تطبيق تحسينات الأداء
            self._optimize_performance_settings()
            
            # بدء المراقبة
            self._start_monitoring()
            
            self.is_active = True
            logger.info("✅ Stability system activated")
            
        except Exception as e:
            logger.error(f"❌ Error initializing stability system: {e}")

    def _optimize_memory_settings(self):
        """تحسين إعدادات الذاكرة"""
        try:
            # تحسين garbage collection
            gc.set_threshold(700, 10, 10)  # أكثر عدوانية
            
            # تحسين ذاكرة التخزين المؤقت لـ Kivy
            Cache.register('kv.image', limit=20, timeout=300)  # 20 صورة لمدة 5 دقائق
            Cache.register('kv.texture', limit=15, timeout=180)  # 15 texture لمدة 3 دقائق
            
            logger.info("🧠 Memory settings optimized")
            
        except Exception as e:
            logger.error(f"Error optimizing memory: {e}")

    def _optimize_performance_settings(self):
        """تحسين إعدادات الأداء"""
        try:
            # تحسين إعدادات Python
            sys.setswitchinterval(0.005)  # تحسين thread switching
            
            # تحسين إعدادات الصوت
            os.environ.setdefault('KIVY_AUDIO_BUFFER_SIZE', '4096')
            os.environ.setdefault('SDL_AUDIODRIVER', 'pulse')
            
            logger.info("⚡ Performance settings optimized")
            
        except Exception as e:
            logger.error(f"Error optimizing performance: {e}")

    def _start_monitoring(self):
        """بدء مراقبة النظام"""
        try:
            # مراقبة دورية كل 15 ثانية
            Clock.schedule_interval(self._monitor_system, 15)
            
            # تنظيف الذاكرة كل 45 ثانية
            Clock.schedule_interval(self._smart_memory_cleanup, 45)
            
            # فحص الأداء كل دقيقة
            Clock.schedule_interval(self._performance_check, 60)
            
            logger.info("👁️ System monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")

    def _monitor_system(self, dt):
        """مراقبة حالة النظام"""
        try:
            # فحص استهلاك الذاكرة
            memory_usage = self._get_memory_usage()
            
            # فحص عدد الكائنات
            object_count = len(gc.get_objects())
            
            # تحذير إذا كان الاستهلاك عالي
            if memory_usage > 80 or object_count > 50000:
                logger.warning(f"⚠️ High resource usage: Memory {memory_usage}%, Objects {object_count}")
                self._emergency_cleanup()
                
            # تحديث الإحصائيات
            self._update_stats()
            
        except Exception as e:
            logger.debug(f"Monitor error: {e}")

    def _get_memory_usage(self):
        """الحصول على نسبة استهلاك الذاكرة"""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except ImportError:
            # تقدير تقريبي بدون psutil
            return min(100, len(gc.get_objects()) / 1000)

    def _smart_memory_cleanup(self, dt):
        """تنظيف ذكي للذاكرة"""
        try:
            # تنظيف عادي
            collected = gc.collect()
            
            # تنظيف ذاكرة Kivy
            try:
                Cache.remove('kv.image')
                Cache.remove('kv.texture')
            except:
                pass
                
            # تنظيف ذاكرة التطبيق
            if hasattr(self.app, '_cover_cache'):
                cache_size = len(self.app._cover_cache)
                if cache_size > 30:
                    # إزالة النصف الأقدم
                    items = list(self.app._cover_cache.items())
                    for key, _ in items[:cache_size//2]:
                        del self.app._cover_cache[key]
                        
            self.stats['memory_cleanups'] += 1
            logger.debug(f"🧹 Smart cleanup: {collected} objects collected")
            
        except Exception as e:
            logger.debug(f"Cleanup error: {e}")

    def _emergency_cleanup(self):
        """تنظيف طارئ للذاكرة"""
        try:
            logger.warning("🚨 Emergency memory cleanup initiated")
            
            # تنظيف قوي
            for _ in range(3):
                gc.collect()
                
            # تنظيف جميع ذاكرات التخزين المؤقت
            try:
                Cache.remove('kv.image')
                Cache.remove('kv.texture')
                Cache.remove('kv.loader')
            except:
                pass
                
            # تنظيف ذاكرة التطبيق
            if hasattr(self.app, '_cover_cache'):
                self.app._cover_cache.clear()
            if hasattr(self.app, '_metadata_cache'):
                self.app._metadata_cache.clear()
                
            self.stats['crashes_prevented'] += 1
            logger.info("✅ Emergency cleanup completed")
            
        except Exception as e:
            logger.error(f"Emergency cleanup failed: {e}")

    def _performance_check(self, dt):
        """فحص الأداء"""
        try:
            # قياس زمن الاستجابة
            start_time = time.time()
            
            # عملية بسيطة لقياس الاستجابة
            test_list = list(range(1000))
            test_sum = sum(test_list)
            
            response_time = (time.time() - start_time) * 1000  # بالميلي ثانية
            
            # تحديث متوسط زمن الاستجابة
            if self.stats['average_response_time'] == 0:
                self.stats['average_response_time'] = response_time
            else:
                self.stats['average_response_time'] = (
                    self.stats['average_response_time'] * 0.8 + response_time * 0.2
                )
                
            # تحذير إذا كان الأداء بطيء
            if response_time > 100:  # أكثر من 100ms
                logger.warning(f"⚠️ Slow performance detected: {response_time:.1f}ms")
                self._optimize_performance()
                
        except Exception as e:
            logger.debug(f"Performance check error: {e}")

    def _optimize_performance(self):
        """تحسين الأداء"""
        try:
            # تنظيف فوري للذاكرة
            gc.collect()
            
            # تقليل جودة الصور مؤقتاً
            if hasattr(self.app, 'image_optimizer'):
                self.app.image_optimizer.set_quality_mode('performance')
                
            # تقليل تكرار العمليات
            self._reduce_operation_frequency()
            
            self.stats['performance_optimizations'] += 1
            logger.info("⚡ Performance optimization applied")
            
        except Exception as e:
            logger.error(f"Performance optimization failed: {e}")

    def _reduce_operation_frequency(self):
        """تقليل تكرار العمليات"""
        try:
            # تقليل تكرار تحديث التقدم
            if hasattr(self.app, 'progress_update_interval'):
                self.app.progress_update_interval = max(0.5, 
                    getattr(self.app, 'progress_update_interval', 0.1) * 1.5)
                
        except Exception as e:
            logger.debug(f"Error reducing operation frequency: {e}")

    def _update_stats(self):
        """تحديث الإحصائيات"""
        try:
            # حساب وقت التشغيل
            if not hasattr(self, '_start_time'):
                self._start_time = time.time()
            
            uptime = time.time() - self._start_time
            
            # تحديث إحصائيات أخرى حسب الحاجة
            
        except Exception as e:
            logger.debug(f"Stats update error: {e}")

    def error_handler(self, func):
        """مُزخرف لمعالجة الأخطاء"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                self.stats['errors_count'] += 1
                logger.error(f"❌ Error in {func.__name__}: {e}")
                
                # محاولة استعادة تلقائية
                if self.settings['auto_recovery']:
                    self._attempt_recovery(func.__name__, e)
                    
                return None
        return wrapper

    def _attempt_recovery(self, function_name, error):
        """محاولة استعادة تلقائية"""
        try:
            logger.info(f"🔄 Attempting recovery for {function_name}")
            
            # تنظيف الذاكرة
            gc.collect()
            
            # إعادة تعيين بعض المتغيرات
            if hasattr(self.app, 'sound') and 'sound' in function_name.lower():
                self.app.sound = None
                self.app.is_playing = False
                
            logger.info("✅ Recovery attempt completed")
            
        except Exception as e:
            logger.error(f"Recovery failed: {e}")

    def get_system_report(self):
        """الحصول على تقرير النظام"""
        try:
            memory_usage = self._get_memory_usage()
            object_count = len(gc.get_objects())
            
            return {
                'status': 'active' if self.is_active else 'inactive',
                'performance_mode': self.performance_mode,
                'memory_usage': f"{memory_usage:.1f}%",
                'object_count': object_count,
                'stats': self.stats.copy(),
                'settings': self.settings.copy(),
                'uptime': getattr(self, '_start_time', 0)
            }
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return {'status': 'error', 'error': str(e)}

    def optimize_for_device(self, device_type='auto'):
        """تحسين النظام حسب نوع الجهاز"""
        try:
            if device_type == 'low_end':
                self.performance_mode = 'conservative'
                # إعدادات متحفظة للأجهزة الضعيفة
                Cache.register('kv.image', limit=10, timeout=120)
                gc.set_threshold(500, 5, 5)
                
            elif device_type == 'high_end':
                self.performance_mode = 'aggressive'
                # إعدادات قوية للأجهزة القوية
                Cache.register('kv.image', limit=50, timeout=600)
                gc.set_threshold(1000, 15, 15)
                
            else:  # auto
                self.performance_mode = 'balanced'
                # إعدادات متوازنة
                Cache.register('kv.image', limit=25, timeout=300)
                gc.set_threshold(700, 10, 10)
                
            logger.info(f"🔧 Optimized for {device_type} device")
            
        except Exception as e:
            logger.error(f"Device optimization failed: {e}")

    def shutdown(self):
        """إيقاف النظام"""
        try:
            # إلغاء المراقبة
            Clock.unschedule(self._monitor_system)
            Clock.unschedule(self._smart_memory_cleanup)
            Clock.unschedule(self._performance_check)
            
            # تنظيف نهائي
            gc.collect()
            
            self.is_active = False
            logger.info("🛑 Enhanced Stability System shutdown")
            
        except Exception as e:
            logger.error(f"Shutdown error: {e}")

# دالة مساعدة لتطبيق النظام على التطبيق
def apply_enhanced_stability(app):
    """تطبيق نظام الاستقرار المحسن على التطبيق"""
    try:
        if not hasattr(app, 'enhanced_stability'):
            app.enhanced_stability = EnhancedStabilitySystem(app)
            logger.info("🚀 Enhanced stability applied to app")
        return app.enhanced_stability
    except Exception as e:
        logger.error(f"Failed to apply enhanced stability: {e}")
        return None
