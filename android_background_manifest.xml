<?xml version="1.0" encoding="utf-8"?>
<!-- Android Manifest additions for background playback -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.arabicplayer.arabicplayer">

    <!-- Permissions for background playback -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Features -->
    <uses-feature android:name="android.hardware.audio.output" android:required="true" />
    <uses-feature android:name="android.software.leanback" android:required="false" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/icon"
        android:label="@string/app_name"
        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
        android:hardwareAccelerated="true"
        android:requestLegacyExternalStorage="true">

        <!-- Main Activity -->
        <activity
            android:name="org.kivy.android.PythonActivity"
            android:label="@string/app_name"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|uiMode"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            
            <!-- Media button intents -->
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </activity>

        <!-- Background Music Service -->
        <service
            android:name="org.arabicplayer.BackgroundMusicService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback">
            <intent-filter>
                <action android:name="org.arabicplayer.BACKGROUND_MUSIC" />
            </intent-filter>
        </service>

        <!-- Media Control Receiver -->
        <receiver
            android:name="org.arabicplayer.MediaControlReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="com.arabicplayer.MEDIA_CONTROL" />
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

        <!-- Boot Receiver for auto-start -->
        <receiver
            android:name="org.arabicplayer.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- Media Session for lock screen controls -->
        <service
            android:name="androidx.media.session.MediaButtonReceiver$MediaButtonConnectionCallback"
            android:enabled="true"
            android:exported="false" />

        <!-- Notification channels (Android 8.0+) -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <!-- Media metadata -->
        <meta-data
            android:name="android.media.browse.MediaBrowserService"
            android:value="org.arabicplayer.BackgroundMusicService" />

    </application>

</manifest>
