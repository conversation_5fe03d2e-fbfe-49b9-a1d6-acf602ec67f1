"""
خدمة التشغيل في الخلفية لمشغل الموسيقى العربي
Background service for Arabic Music Player
"""

import os
import logging
from kivy.utils import platform

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BackgroundMusicService:
    """خدمة تشغيل الموسيقى في الخلفية"""
    
    def __init__(self, app_instance=None):
        self.app = app_instance
        self.is_running = False
        self.notification_id = 1001
        self.service_running = False
        
        # Android components
        self.notification_manager = None
        self.notification_builder = None
        self.media_session = None
        self.wake_lock = None
        
        # تحميل مكونات الأندرويد
        self._load_android_components()
        
        logger.info("BackgroundMusicService initialized")
    
    def _load_android_components(self):
        """تحميل مكونات الأندرويد للخدمة"""
        if platform != 'android':
            logger.info("Not running on Android - background service disabled")
            return
        
        try:
            from jnius import autoclass, cast
            
            # Android classes
            self.PythonActivity = autoclass('org.kivy.android.PythonActivity')
            self.Context = autoclass('android.content.Context')
            self.Intent = autoclass('android.content.Intent')
            self.PendingIntent = autoclass('android.app.PendingIntent')
            self.NotificationManager = autoclass('android.app.NotificationManager')
            self.NotificationChannel = autoclass('android.app.NotificationChannel')
            self.NotificationCompat = autoclass('androidx.core.app.NotificationCompat')
            self.MediaSession = autoclass('android.media.session.MediaSession')
            self.PowerManager = autoclass('android.os.PowerManager')
            
            # Get activity and context
            self.activity = self.PythonActivity.mActivity
            self.context = cast('android.content.Context', self.activity)
            
            logger.info("✅ Android components loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load Android components: {e}")
            return False
    
    def create_notification_channel(self):
        """إنشاء قناة الإشعارات"""
        try:
            if not self.NotificationManager:
                return False
            
            channel_id = "music_player_channel"
            channel_name = "مشغل الموسيقى العربي"
            channel_description = "إشعارات تشغيل الموسيقى"
            
            # إنشاء القناة
            channel = self.NotificationChannel(
                channel_id,
                channel_name,
                self.NotificationManager.IMPORTANCE_LOW
            )
            channel.setDescription(channel_description)
            channel.setSound(None, None)  # بدون صوت
            
            # تسجيل القناة
            notification_manager = self.context.getSystemService(self.Context.NOTIFICATION_SERVICE)
            notification_manager.createNotificationChannel(channel)
            
            logger.info("✅ Notification channel created")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create notification channel: {e}")
            return False
    
    def create_media_session(self):
        """إنشاء جلسة الوسائط للتحكم"""
        try:
            if not self.MediaSession:
                return False
            
            # إنشاء جلسة الوسائط
            self.media_session = self.MediaSession(self.context, "ArabicMusicPlayer")
            
            # إعداد callback للتحكم
            callback = MediaSessionCallback(self.app)
            self.media_session.setCallback(callback)
            
            # تفعيل الجلسة
            self.media_session.setActive(True)
            
            logger.info("✅ Media session created")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create media session: {e}")
            return False
    
    def acquire_wake_lock(self):
        """الحصول على wake lock لمنع النوم"""
        try:
            if not self.PowerManager:
                return False
            
            power_manager = self.context.getSystemService(self.Context.POWER_SERVICE)
            self.wake_lock = power_manager.newWakeLock(
                self.PowerManager.PARTIAL_WAKE_LOCK,
                "ArabicMusicPlayer:BackgroundPlayback"
            )
            self.wake_lock.acquire()
            
            logger.info("✅ Wake lock acquired")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to acquire wake lock: {e}")
            return False
    
    def release_wake_lock(self):
        """تحرير wake lock"""
        try:
            if self.wake_lock and self.wake_lock.isHeld():
                self.wake_lock.release()
                logger.info("✅ Wake lock released")
                return True
        except Exception as e:
            logger.error(f"❌ Failed to release wake lock: {e}")
        return False
    
    def create_notification(self, title="مشغل الموسيقى العربي", text="يتم التشغيل...", is_playing=True):
        """إنشاء إشعار التحكم في الموسيقى"""
        try:
            if not self.NotificationCompat:
                return None
            
            # إنشاء intent للعودة للتطبيق
            intent = self.Intent(self.context, self.PythonActivity)
            intent.setFlags(self.Intent.FLAG_ACTIVITY_SINGLE_TOP)
            
            pending_intent = self.PendingIntent.getActivity(
                self.context, 0, intent, 
                self.PendingIntent.FLAG_UPDATE_CURRENT | self.PendingIntent.FLAG_IMMUTABLE
            )
            
            # إنشاء الإشعار
            builder = self.NotificationCompat.Builder(self.context, "music_player_channel")
            builder.setContentTitle(title)
            builder.setContentText(text)
            builder.setSmallIcon(android.R.drawable.ic_media_play)  # أيقونة افتراضية
            builder.setContentIntent(pending_intent)
            builder.setOngoing(True)  # إشعار دائم
            builder.setPriority(self.NotificationCompat.PRIORITY_LOW)
            
            # إضافة أزرار التحكم
            self._add_media_buttons(builder, is_playing)
            
            # إضافة معلومات الوسائط
            if self.media_session:
                builder.setStyle(
                    self.NotificationCompat.MediaStyle()
                    .setMediaSession(self.media_session.getSessionToken())
                    .setShowActionsInCompactView(0, 1, 2)  # عرض 3 أزرار
                )
            
            notification = builder.build()
            logger.info("✅ Notification created")
            return notification
            
        except Exception as e:
            logger.error(f"❌ Failed to create notification: {e}")
            return None
    
    def _add_media_buttons(self, builder, is_playing):
        """إضافة أزرار التحكم للإشعار"""
        try:
            # زر السابق
            prev_intent = self._create_media_intent("PREVIOUS")
            prev_pending = self.PendingIntent.getBroadcast(
                self.context, 1, prev_intent, 
                self.PendingIntent.FLAG_UPDATE_CURRENT | self.PendingIntent.FLAG_IMMUTABLE
            )
            builder.addAction(
                android.R.drawable.ic_media_previous,
                "السابق", prev_pending
            )
            
            # زر تشغيل/إيقاف
            play_pause_action = "PAUSE" if is_playing else "PLAY"
            play_pause_icon = android.R.drawable.ic_media_pause if is_playing else android.R.drawable.ic_media_play
            play_pause_text = "إيقاف" if is_playing else "تشغيل"
            
            play_pause_intent = self._create_media_intent(play_pause_action)
            play_pause_pending = self.PendingIntent.getBroadcast(
                self.context, 2, play_pause_intent,
                self.PendingIntent.FLAG_UPDATE_CURRENT | self.PendingIntent.FLAG_IMMUTABLE
            )
            builder.addAction(play_pause_icon, play_pause_text, play_pause_pending)
            
            # زر التالي
            next_intent = self._create_media_intent("NEXT")
            next_pending = self.PendingIntent.getBroadcast(
                self.context, 3, next_intent,
                self.PendingIntent.FLAG_UPDATE_CURRENT | self.PendingIntent.FLAG_IMMUTABLE
            )
            builder.addAction(
                android.R.drawable.ic_media_next,
                "التالي", next_pending
            )
            
            # زر إغلاق
            close_intent = self._create_media_intent("CLOSE")
            close_pending = self.PendingIntent.getBroadcast(
                self.context, 4, close_intent,
                self.PendingIntent.FLAG_UPDATE_CURRENT | self.PendingIntent.FLAG_IMMUTABLE
            )
            builder.addAction(
                android.R.drawable.ic_menu_close_clear_cancel,
                "إغلاق", close_pending
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to add media buttons: {e}")
    
    def _create_media_intent(self, action):
        """إنشاء intent للتحكم في الوسائط"""
        try:
            intent = self.Intent("com.arabicplayer.MEDIA_CONTROL")
            intent.putExtra("action", action)
            return intent
        except Exception as e:
            logger.error(f"❌ Failed to create media intent: {e}")
            return None
    
    def start_background_service(self, song_title="أغنية", artist="فنان"):
        """بدء خدمة التشغيل في الخلفية"""
        try:
            if platform != 'android':
                logger.info("Background service not available on this platform")
                return False
            
            if self.service_running:
                logger.info("Background service already running")
                return True
            
            # إنشاء قناة الإشعارات
            self.create_notification_channel()
            
            # إنشاء جلسة الوسائط
            self.create_media_session()
            
            # الحصول على wake lock
            self.acquire_wake_lock()
            
            # إنشاء وعرض الإشعار
            notification = self.create_notification(
                title=song_title,
                text=f"بواسطة {artist}",
                is_playing=True
            )
            
            if notification:
                notification_manager = self.context.getSystemService(self.Context.NOTIFICATION_SERVICE)
                notification_manager.notify(self.notification_id, notification)
                
                self.service_running = True
                logger.info("✅ Background service started")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to start background service: {e}")
            return False
    
    def update_notification(self, song_title="أغنية", artist="فنان", is_playing=True):
        """تحديث إشعار التشغيل"""
        try:
            if not self.service_running:
                return False
            
            notification = self.create_notification(
                title=song_title,
                text=f"بواسطة {artist}",
                is_playing=is_playing
            )
            
            if notification:
                notification_manager = self.context.getSystemService(self.Context.NOTIFICATION_SERVICE)
                notification_manager.notify(self.notification_id, notification)
                logger.info("✅ Notification updated")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to update notification: {e}")
            return False
    
    def stop_background_service(self):
        """إيقاف خدمة التشغيل في الخلفية"""
        try:
            if not self.service_running:
                return True
            
            # إزالة الإشعار
            if self.context:
                notification_manager = self.context.getSystemService(self.Context.NOTIFICATION_SERVICE)
                notification_manager.cancel(self.notification_id)
            
            # إيقاف جلسة الوسائط
            if self.media_session:
                self.media_session.setActive(False)
                self.media_session.release()
                self.media_session = None
            
            # تحرير wake lock
            self.release_wake_lock()
            
            self.service_running = False
            logger.info("✅ Background service stopped")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to stop background service: {e}")
            return False

class MediaSessionCallback:
    """معالج أحداث جلسة الوسائط"""
    
    def __init__(self, app_instance):
        self.app = app_instance
    
    def onPlay(self):
        """عند الضغط على تشغيل"""
        try:
            if self.app and hasattr(self.app, 'root'):
                self.app.root.play_current_song()
            logger.info("Media session: Play")
        except Exception as e:
            logger.error(f"Error in onPlay: {e}")
    
    def onPause(self):
        """عند الضغط على إيقاف"""
        try:
            if self.app and hasattr(self.app, 'root'):
                self.app.root.pause_current_song()
            logger.info("Media session: Pause")
        except Exception as e:
            logger.error(f"Error in onPause: {e}")
    
    def onSkipToNext(self):
        """عند الضغط على التالي"""
        try:
            if self.app and hasattr(self.app, 'root'):
                self.app.root.next_song()
            logger.info("Media session: Next")
        except Exception as e:
            logger.error(f"Error in onSkipToNext: {e}")
    
    def onSkipToPrevious(self):
        """عند الضغط على السابق"""
        try:
            if self.app and hasattr(self.app, 'root'):
                self.app.root.previous_song()
            logger.info("Media session: Previous")
        except Exception as e:
            logger.error(f"Error in onSkipToPrevious: {e}")
    
    def onStop(self):
        """عند الضغط على إيقاف"""
        try:
            if self.app and hasattr(self.app, 'root'):
                self.app.root.stop_current_song()
            logger.info("Media session: Stop")
        except Exception as e:
            logger.error(f"Error in onStop: {e}")

# إنشاء مثيل عام لخدمة الخلفية
background_service = BackgroundMusicService()
