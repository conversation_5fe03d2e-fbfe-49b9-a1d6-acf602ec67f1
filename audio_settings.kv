<Screen name="audio_settings">
    BoxLayout:
        orientation: 'vertical'
        MDTopAppBar:
            title: "🎛️ Advanced Audio Settings"
            left_action_items: [["arrow-left", lambda x: app.root.show_main_screen()]]
            right_action_items: [["restore", lambda x: app.root.reset_audio_filters()]]
            elevation: 10

        ScrollView:
            BoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: dp(16)
                spacing: dp(16)

                # قسم التحكم الأساسي
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)

                    MDLabel:
                        text: "🔊 Audio Enhancement"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]

                    MDSeparator:
                        height: dp(1)

                    BoxLayout:
                        size_hint_y: None
                        height: dp(48)

                        MDLabel:
                            text: "Enable Advanced Audio Filters"

                        MDSwitch:
                            id: advanced_filters_switch
                            active: app.root.advanced_filters_enabled
                            on_active: app.root.toggle_advanced_filters(self.active)

                    BoxLayout:
                        size_hint_y: None
                        height: dp(48)

                        MDLabel:
                            text: "Volume Normalization"

                        MDSwitch:
                            id: volume_normalization_switch
                            active: app.root.volume_normalization
                            on_active: app.root.toggle_volume_normalization(self.active)

                # قسم الإعدادات المسبقة
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)

                    MDLabel:
                        text: "🎼 Audio Presets"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]

                    MDSeparator:
                        height: dp(1)

                    # أزرار الإعدادات المسبقة
                    GridLayout:
                        cols: 2
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(8)

                        MDRaisedButton:
                            text: "🎸 Rock"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('rock')

                        MDRaisedButton:
                            text: "🎤 Pop"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('pop')

                        MDRaisedButton:
                            text: "🎻 Classical"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('classical')

                        MDRaisedButton:
                            text: "🎷 Jazz"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('jazz')

                        MDRaisedButton:
                            text: "🎧 Electronic"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('electronic')

                        MDRaisedButton:
                            text: "🎙️ Vocal"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('vocal')

                        MDRaisedButton:
                            text: "🔊 Bass Boost"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('bass_boost')

                        MDRaisedButton:
                            text: "👂 Audiophile"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.apply_audio_preset('audiophile')

                # قسم الإيكولايزر المتقدم
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)

                    MDLabel:
                        text: "🎛️ Advanced Equalizer (7-Band)"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]

                    MDSeparator:
                        height: dp(1)

                    # Sub Bass (20-60 Hz)
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Sub Bass\n20-60 Hz"
                            size_hint_x: 0.3
                            halign: "center"

                        MDSlider:
                            id: sub_bass_slider
                            min: -20
                            max: 20
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_equalizer('sub_bass', self.value)

                        MDLabel:
                            text: f"{int(sub_bass_slider.value)}dB"
                            size_hint_x: 0.1
                            halign: "center"

                    # Bass (60-250 Hz)
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Bass\n60-250 Hz"
                            size_hint_x: 0.3
                            halign: "center"

                        MDSlider:
                            id: bass_slider
                            min: -20
                            max: 20
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_equalizer('bass', self.value)

                        MDLabel:
                            text: f"{int(bass_slider.value)}dB"
                            size_hint_x: 0.1
                            halign: "center"

                    # Low Mid (250-500 Hz)
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Low Mid\n250-500 Hz"
                            size_hint_x: 0.3
                            halign: "center"

                        MDSlider:
                            id: low_mid_slider
                            min: -20
                            max: 20
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_equalizer('low_mid', self.value)

                        MDLabel:
                            text: f"{int(low_mid_slider.value)}dB"
                            size_hint_x: 0.1
                            halign: "center"

                    # Mid (500-2000 Hz)
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Mid\n500-2000 Hz"
                            size_hint_x: 0.3
                            halign: "center"

                        MDSlider:
                            id: mid_slider
                            min: -20
                            max: 20
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_equalizer('mid', self.value)

                        MDLabel:
                            text: f"{int(mid_slider.value)}dB"
                            size_hint_x: 0.1
                            halign: "center"

                    # High Mid (2000-4000 Hz)
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "High Mid\n2-4 kHz"
                            size_hint_x: 0.3
                            halign: "center"

                        MDSlider:
                            id: high_mid_slider
                            min: -20
                            max: 20
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_equalizer('high_mid', self.value)

                        MDLabel:
                            text: f"{int(high_mid_slider.value)}dB"
                            size_hint_x: 0.1
                            halign: "center"

                    # Presence (4000-6000 Hz)
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Presence\n4-6 kHz"
                            size_hint_x: 0.3
                            halign: "center"

                        MDSlider:
                            id: presence_slider
                            min: -20
                            max: 20
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_equalizer('presence', self.value)

                        MDLabel:
                            text: f"{int(presence_slider.value)}dB"
                            size_hint_x: 0.1
                            halign: "center"

                    # Brilliance (6000-20000 Hz)
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Brilliance\n6-20 kHz"
                            size_hint_x: 0.3
                            halign: "center"

                        MDSlider:
                            id: brilliance_slider
                            min: -20
                            max: 20
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_equalizer('brilliance', self.value)

                        MDLabel:
                            text: f"{int(brilliance_slider.value)}dB"
                            size_hint_x: 0.1
                            halign: "center"

                # قسم فلاتر التحسين
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)

                    MDLabel:
                        text: "🔊 Enhancement Filters"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]

                    MDSeparator:
                        height: dp(1)

                    # Bass Boost
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Bass Boost"
                            size_hint_x: 0.3

                        MDSlider:
                            id: bass_boost_slider
                            min: 0
                            max: 1
                            value: 0
                            size_hint_x: 0.6
                            on_value: app.root.update_enhancement('bass_boost', self.value)

                        MDLabel:
                            text: f"{int(bass_boost_slider.value * 100)}%"
                            size_hint_x: 0.1
                            halign: "center"

                    # Stereo Enhancement
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Stereo Width"
                            size_hint_x: 0.3

                        MDSlider:
                            id: stereo_slider
                            min: 0
                            max: 1
                            value: 0.2
                            size_hint_x: 0.6
                            on_value: app.root.update_enhancement('stereo_enhancement', self.value)

                        MDLabel:
                            text: f"{int(stereo_slider.value * 100)}%"
                            size_hint_x: 0.1
                            halign: "center"

                    # Dynamic Range Compression
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Compression"
                            size_hint_x: 0.3

                        MDSlider:
                            id: compression_slider
                            min: 0
                            max: 1
                            value: 0.3
                            size_hint_x: 0.6
                            on_value: app.root.update_enhancement('dynamic_range_compression', self.value)

                        MDLabel:
                            text: f"{int(compression_slider.value * 100)}%"
                            size_hint_x: 0.1
                            halign: "center"

                    # Noise Reduction
                    BoxLayout:
                        size_hint_y: None
                        height: dp(60)
                        spacing: dp(10)

                        MDLabel:
                            text: "Noise Reduction"
                            size_hint_x: 0.3

                        MDSlider:
                            id: noise_reduction_slider
                            min: 0
                            max: 1
                            value: 0.1
                            size_hint_x: 0.6
                            on_value: app.root.update_enhancement('noise_reduction', self.value)

                        MDLabel:
                            text: f"{int(noise_reduction_slider.value * 100)}%"
                            size_hint_x: 0.1
                            halign: "center"

                    # Vocal Enhancement
                    BoxLayout:
                        size_hint_y: None
                        height: dp(48)

                        MDLabel:
                            text: "Vocal Enhancement"

                        MDSwitch:
                            id: vocal_enhancement_switch
                            active: False
                            on_active: app.root.update_enhancement('vocal_enhancement', self.active)

                # قسم إعدادات الجودة
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)

                    MDLabel:
                        text: "🎵 Quality Settings"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]

                    MDSeparator:
                        height: dp(1)

                    # Quality Mode
                    BoxLayout:
                        size_hint_y: None
                        height: dp(48)
                        spacing: dp(10)

                        MDLabel:
                            text: "Quality Mode:"
                            size_hint_x: 0.4

                        MDSelectionControl:
                            id: quality_mode_selection
                            size_hint_x: 0.6

                    GridLayout:
                        cols: 2
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(8)

                        MDRaisedButton:
                            text: "Ultra (96kHz)"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.set_quality_mode('ultra')

                        MDRaisedButton:
                            text: "High (48kHz)"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.set_quality_mode('high')

                        MDRaisedButton:
                            text: "Medium (44kHz)"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.set_quality_mode('medium')

                        MDRaisedButton:
                            text: "Low (44kHz)"
                            size_hint_y: None
                            height: dp(40)
                            on_release: app.root.set_quality_mode('low')
                
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)
                    
                    MDLabel:
                        text: "Equalizer"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDSeparator:
                        height: dp(1)
                        
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        BoxLayout:
                            size_hint_y: None
                            height: dp(20)
                            
                            MDLabel:
                                text: "Bass"
                                size_hint_x: 0.3
                                
                            MDLabel:
                                id: bass_value_label
                                text: str(int(bass_slider.value)) + " dB"
                                halign: 'right'
                                size_hint_x: 0.2
                                
                        MDSlider:
                            id: bass_slider
                            min: -10
                            max: 10
                            step: 1
                            value: app.root.bass_level
                            on_value: app.root.set_bass_level(self.value)
                            
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        BoxLayout:
                            size_hint_y: None
                            height: dp(20)
                            
                            MDLabel:
                                text: "Mid"
                                size_hint_x: 0.3
                                
                            MDLabel:
                                id: mid_value_label
                                text: str(int(mid_slider.value)) + " dB"
                                halign: 'right'
                                size_hint_x: 0.2
                                
                        MDSlider:
                            id: mid_slider
                            min: -10
                            max: 10
                            step: 1
                            value: app.root.mid_level
                            on_value: app.root.set_mid_level(self.value)
                            
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        BoxLayout:
                            size_hint_y: None
                            height: dp(20)
                            
                            MDLabel:
                                text: "Treble"
                                size_hint_x: 0.3
                                
                            MDLabel:
                                id: treble_value_label
                                text: str(int(treble_slider.value)) + " dB"
                                halign: 'right'
                                size_hint_x: 0.2
                                
                        MDSlider:
                            id: treble_slider
                            min: -10
                            max: 10
                            step: 1
                            value: app.root.treble_level
                            on_value: app.root.set_treble_level(self.value)
                            
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)
                    
                    MDLabel:
                        text: "Audio Presets"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDSeparator:
                        height: dp(1)
                        
                    GridLayout:
                        cols: 2
                        spacing: dp(8)
                        size_hint_y: None
                        height: self.minimum_height
                        
                        MDRaisedButton:
                            text: "Normal"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("normal")
                            
                        MDRaisedButton:
                            text: "Bass Boost"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("bass_boost")
                            
                        MDRaisedButton:
                            text: "Treble Boost"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("treble_boost")
                            
                        MDRaisedButton:
                            text: "Vocal Boost"
                            size_hint_x: 0.5
                            on_release: app.root.apply_audio_preset("vocal_boost")
                            
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(16)
                    spacing: dp(8)
                    
                    MDLabel:
                        text: "Buffer Size"
                        font_style: 'H6'
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDSeparator:
                        height: dp(1)
                        
                    BoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(60)
                        
                        MDLabel:
                            text: "Larger buffer size improves audio quality but increases latency"
                            size_hint_y: None
                            height: self.texture_size[1]
                            font_style: 'Caption'
                            
                        MDDropDownItem:
                            id: buffer_size_dropdown
                            text: "8192 bytes"
                            on_release: app.root.show_buffer_size_menu()
