#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف ZIP للتطبيق العربي لتحويله إلى APK
Arabic Music Player - APK ZIP Creator
"""

import os
import zipfile
import shutil
from datetime import datetime

def create_apk_zip():
    """إنشاء ملف ZIP يحتوي على جميع الملفات المطلوبة لتحويل التطبيق إلى APK"""
    
    # اسم ملف ZIP
    zip_filename = f"ArabicMusicPlayer_APK_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    
    # الملفات والمجلدات المطلوبة
    required_files = [
        # الملفات الأساسية
        'main.py',
        'buildozer.spec',
        
        # ملفات KV
        'MusicPlayer.kv',
        'download_screen.kv',
        'search_screen.kv',
        'custom_slider.kv',
        
        # الملفات المساعدة
        'arabic_utils.py',
        'audio_enhancer.py',
        'color_circle.py',
        'custom_slider.py',
        'download_manager.py',
        'download_screen.py',
        'performance_optimizer.py',
        'permission_manager.py',
        'playing_indicator.py',
        'search_screen.py',
        
        # ملفات البيانات
        'favorites.json',
        'theme.json',
        'enhanced_theme.json',
        
        # ملف Android Manifest
        'AndroidManifest.template.xml',
    ]
    
    # المجلدات المطلوبة
    required_directories = [
        'fonts',
        'assets',
        'images',
        'music',
        'downloads',
        'album_covers',
        'default_covers'
    ]
    
    print(f"🎵 إنشاء ملف ZIP للتطبيق العربي: {zip_filename}")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            
            # إضافة الملفات الأساسية
            print("📁 إضافة الملفات الأساسية...")
            for file_path in required_files:
                if os.path.exists(file_path):
                    zipf.write(file_path, file_path)
                    print(f"  ✅ تم إضافة: {file_path}")
                else:
                    print(f"  ⚠️ ملف غير موجود: {file_path}")
            
            # إضافة المجلدات
            print("\n📂 إضافة المجلدات...")
            for directory in required_directories:
                if os.path.exists(directory):
                    for root, dirs, files in os.walk(directory):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = file_path.replace('\\', '/')  # تحويل مسارات Windows
                            zipf.write(file_path, arcname)
                    print(f"  ✅ تم إضافة مجلد: {directory}")
                else:
                    print(f"  ⚠️ مجلد غير موجود: {directory}")
            
            # إضافة ملفات buildozer إضافية إذا كانت موجودة
            print("\n🔧 إضافة ملفات buildozer الإضافية...")
            buildozer_files = [
                'buildozer_android_complete.spec',
                'buildozer_full_compatible.spec',
                'buildozer_minimal_guaranteed.spec',
                'buildozer_ndk25_fixed.spec'
            ]
            
            for buildozer_file in buildozer_files:
                if os.path.exists(buildozer_file):
                    zipf.write(buildozer_file, buildozer_file)
                    print(f"  ✅ تم إضافة: {buildozer_file}")
            
            # إضافة ملفات التوثيق
            print("\n📚 إضافة ملفات التوثيق...")
            doc_files = [
                'README_APK_BUILD.md',
                'ANDROID_TROUBLESHOOTING_GUIDE.md',
                'COLAB_UPLOAD_GUIDE.md',
                'ALL_REQUIRED_LIBRARIES.md'
            ]
            
            for doc_file in doc_files:
                if os.path.exists(doc_file):
                    zipf.write(doc_file, doc_file)
                    print(f"  ✅ تم إضافة: {doc_file}")
            
            # إنشاء ملف README للـ ZIP
            readme_content = f"""# Arabic Music Player - APK Build Package

هذا الملف يحتوي على جميع الملفات المطلوبة لتحويل تطبيق مشغل الموسيقى العربي إلى APK.

## محتويات الحزمة:
- main.py: الملف الرئيسي للتطبيق
- buildozer.spec: ملف إعدادات buildozer
- جميع ملفات KV للواجهة
- المكتبات المساعدة
- الخطوط العربية
- الصور والأيقونات
- ملفات التوثيق

## خطوات التحويل إلى APK:
1. ارفع هذا الملف إلى Google Colab
2. فك ضغط الملف
3. قم بتشغيل buildozer android debug
4. انتظر حتى اكتمال البناء
5. حمل ملف APK الناتج

## متطلبات النظام:
- Python 3.11
- Android NDK 25
- جميع المكتبات المدرجة في buildozer.spec

تم إنشاء هذه الحزمة في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            zipf.writestr('README_ZIP.md', readme_content)
            print("  ✅ تم إضافة: README_ZIP.md")
    
        print("\n" + "=" * 60)
        print(f"🎉 تم إنشاء ملف ZIP بنجاح: {zip_filename}")
        
        # عرض معلومات الملف
        file_size = os.path.getsize(zip_filename)
        file_size_mb = file_size / (1024 * 1024)
        print(f"📊 حجم الملف: {file_size_mb:.2f} MB")
        
        # عرض محتويات الملف
        print(f"\n📋 محتويات ملف ZIP:")
        with zipfile.ZipFile(zip_filename, 'r') as zipf:
            file_list = zipf.namelist()
            print(f"   عدد الملفات: {len(file_list)}")
            
            # عرض أول 20 ملف
            for i, file_name in enumerate(file_list[:20]):
                print(f"   {i+1:2d}. {file_name}")
            
            if len(file_list) > 20:
                print(f"   ... و {len(file_list) - 20} ملف آخر")
        
        print(f"\n✅ الملف جاهز للرفع إلى Google Colab لتحويله إلى APK!")
        return zip_filename
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف ZIP: {e}")
        return None

if __name__ == "__main__":
    create_apk_zip()
