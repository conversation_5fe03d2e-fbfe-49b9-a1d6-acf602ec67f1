"""
معالج إشعارات التحكم في الموسيقى
Music control notification receiver
"""

import logging
from kivy.utils import platform

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MusicControlReceiver:
    """معالج أوامر التحكم في الموسيقى من الإشعارات"""
    
    def __init__(self, app_instance=None):
        self.app = app_instance
        self.receiver = None
        self.is_registered = False
        
        # تسجيل معالج الإشعارات
        self._register_receiver()
        
        logger.info("MusicControlReceiver initialized")
    
    def _register_receiver(self):
        """تسجيل معالج البث للإشعارات"""
        if platform != 'android':
            logger.info("Not running on Android - receiver disabled")
            return
        
        try:
            from android.broadcast import BroadcastReceiver
            from jnius import autoclass
            
            # إنشاء معالج البث
            self.receiver = MusicBroadcastReceiver(self.app)
            
            # تسجيل المعالج
            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            IntentFilter = autoclass('android.content.IntentFilter')
            
            activity = PythonActivity.mActivity
            intent_filter = IntentFilter("com.arabicplayer.MEDIA_CONTROL")
            
            activity.registerReceiver(self.receiver, intent_filter)
            self.is_registered = True
            
            logger.info("✅ Broadcast receiver registered")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to register receiver: {e}")
            return False
    
    def unregister_receiver(self):
        """إلغاء تسجيل معالج البث"""
        try:
            if self.is_registered and self.receiver:
                from jnius import autoclass
                
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                activity = PythonActivity.mActivity
                activity.unregisterReceiver(self.receiver)
                
                self.is_registered = False
                logger.info("✅ Broadcast receiver unregistered")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to unregister receiver: {e}")
        
        return False

class MusicBroadcastReceiver:
    """معالج البث للتحكم في الموسيقى"""
    
    def __init__(self, app_instance):
        self.app = app_instance
    
    def onReceive(self, context, intent):
        """معالجة الأوامر الواردة من الإشعارات"""
        try:
            action = intent.getStringExtra("action")
            logger.info(f"Received media control action: {action}")
            
            if not self.app or not hasattr(self.app, 'root'):
                logger.warning("App instance not available")
                return
            
            # تنفيذ الأمر المطلوب
            if action == "PLAY":
                self._handle_play()
            elif action == "PAUSE":
                self._handle_pause()
            elif action == "NEXT":
                self._handle_next()
            elif action == "PREVIOUS":
                self._handle_previous()
            elif action == "CLOSE":
                self._handle_close()
            else:
                logger.warning(f"Unknown action: {action}")
                
        except Exception as e:
            logger.error(f"❌ Error in onReceive: {e}")
    
    def _handle_play(self):
        """معالجة أمر التشغيل"""
        try:
            if hasattr(self.app.root, 'play_current_song'):
                self.app.root.play_current_song()
            elif hasattr(self.app.root, 'play_pause_song'):
                self.app.root.play_pause_song()
            logger.info("✅ Play command executed")
        except Exception as e:
            logger.error(f"❌ Error executing play: {e}")
    
    def _handle_pause(self):
        """معالجة أمر الإيقاف"""
        try:
            if hasattr(self.app.root, 'pause_current_song'):
                self.app.root.pause_current_song()
            elif hasattr(self.app.root, 'play_pause_song'):
                self.app.root.play_pause_song()
            logger.info("✅ Pause command executed")
        except Exception as e:
            logger.error(f"❌ Error executing pause: {e}")
    
    def _handle_next(self):
        """معالجة أمر التالي"""
        try:
            if hasattr(self.app.root, 'next_song'):
                self.app.root.next_song()
            logger.info("✅ Next command executed")
        except Exception as e:
            logger.error(f"❌ Error executing next: {e}")
    
    def _handle_previous(self):
        """معالجة أمر السابق"""
        try:
            if hasattr(self.app.root, 'previous_song'):
                self.app.root.previous_song()
            logger.info("✅ Previous command executed")
        except Exception as e:
            logger.error(f"❌ Error executing previous: {e}")
    
    def _handle_close(self):
        """معالجة أمر الإغلاق"""
        try:
            # إيقاف التشغيل
            if hasattr(self.app.root, 'stop_current_song'):
                self.app.root.stop_current_song()
            
            # إيقاف خدمة الخلفية
            if hasattr(self.app.root, 'background_service'):
                self.app.root.background_service.stop_background_service()
            
            logger.info("✅ Close command executed")
        except Exception as e:
            logger.error(f"❌ Error executing close: {e}")

# === FLOATING CONTROLS ===
class FloatingControls:
    """أزرار التحكم العائمة"""
    
    def __init__(self, app_instance=None):
        self.app = app_instance
        self.floating_view = None
        self.is_showing = False
        
        logger.info("FloatingControls initialized")
    
    def create_floating_controls(self):
        """إنشاء أزرار التحكم العائمة"""
        if platform != 'android':
            logger.info("Floating controls not available on this platform")
            return False
        
        try:
            from jnius import autoclass, cast
            
            # Android classes
            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            WindowManager = autoclass('android.view.WindowManager')
            LayoutParams = autoclass('android.view.WindowManager$LayoutParams')
            LinearLayout = autoclass('android.widget.LinearLayout')
            Button = autoclass('android.widget.Button')
            
            activity = PythonActivity.mActivity
            window_manager = activity.getSystemService('window')
            
            # إنشاء layout للأزرار
            layout = LinearLayout(activity)
            layout.setOrientation(LinearLayout.HORIZONTAL)
            
            # إنشاء الأزرار
            prev_btn = Button(activity)
            prev_btn.setText("⏮️")
            layout.addView(prev_btn)
            
            play_pause_btn = Button(activity)
            play_pause_btn.setText("⏯️")
            layout.addView(play_pause_btn)
            
            next_btn = Button(activity)
            next_btn.setText("⏭️")
            layout.addView(next_btn)
            
            close_btn = Button(activity)
            close_btn.setText("✖️")
            layout.addView(close_btn)
            
            # إعداد معاملات النافذة
            params = LayoutParams(
                LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT,
                LayoutParams.TYPE_APPLICATION_OVERLAY,
                LayoutParams.FLAG_NOT_FOCUSABLE,
                -3  # TRANSLUCENT
            )
            
            # إضافة النافذة العائمة
            window_manager.addView(layout, params)
            
            self.floating_view = layout
            self.is_showing = True
            
            logger.info("✅ Floating controls created")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create floating controls: {e}")
            return False
    
    def hide_floating_controls(self):
        """إخفاء أزرار التحكم العائمة"""
        try:
            if self.is_showing and self.floating_view:
                from jnius import autoclass
                
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                activity = PythonActivity.mActivity
                window_manager = activity.getSystemService('window')
                
                window_manager.removeView(self.floating_view)
                self.is_showing = False
                
                logger.info("✅ Floating controls hidden")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to hide floating controls: {e}")
        
        return False

# === BACKGROUND PLAYBACK MANAGER ===
class BackgroundPlaybackManager:
    """مدير التشغيل في الخلفية"""
    
    def __init__(self, app_instance=None):
        self.app = app_instance
        self.background_service = None
        self.notification_receiver = None
        self.floating_controls = None
        self.is_background_enabled = False
        
        # تحميل المكونات
        self._initialize_components()
        
        logger.info("BackgroundPlaybackManager initialized")
    
    def _initialize_components(self):
        """تحميل مكونات التشغيل في الخلفية"""
        try:
            # تحميل خدمة الخلفية
            from background_service import BackgroundMusicService
            self.background_service = BackgroundMusicService(self.app)
            
            # تحميل معالج الإشعارات
            self.notification_receiver = MusicControlReceiver(self.app)
            
            # تحميل أزرار التحكم العائمة
            self.floating_controls = FloatingControls(self.app)
            
            logger.info("✅ Background components initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize background components: {e}")
            return False
    
    def enable_background_playback(self, song_title="أغنية", artist="فنان"):
        """تفعيل التشغيل في الخلفية"""
        try:
            if self.is_background_enabled:
                logger.info("Background playback already enabled")
                return True
            
            # بدء خدمة الخلفية
            if self.background_service:
                success = self.background_service.start_background_service(song_title, artist)
                if success:
                    self.is_background_enabled = True
                    logger.info("✅ Background playback enabled")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to enable background playback: {e}")
            return False
    
    def disable_background_playback(self):
        """إيقاف التشغيل في الخلفية"""
        try:
            if not self.is_background_enabled:
                return True
            
            # إيقاف خدمة الخلفية
            if self.background_service:
                self.background_service.stop_background_service()
            
            # إخفاء أزرار التحكم العائمة
            if self.floating_controls:
                self.floating_controls.hide_floating_controls()
            
            self.is_background_enabled = False
            logger.info("✅ Background playback disabled")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to disable background playback: {e}")
            return False
    
    def update_background_info(self, song_title="أغنية", artist="فنان", is_playing=True):
        """تحديث معلومات التشغيل في الخلفية"""
        try:
            if self.is_background_enabled and self.background_service:
                return self.background_service.update_notification(song_title, artist, is_playing)
            return False
        except Exception as e:
            logger.error(f"❌ Failed to update background info: {e}")
            return False
    
    def show_floating_controls(self):
        """عرض أزرار التحكم العائمة"""
        try:
            if self.floating_controls and not self.floating_controls.is_showing:
                return self.floating_controls.create_floating_controls()
            return False
        except Exception as e:
            logger.error(f"❌ Failed to show floating controls: {e}")
            return False
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            # إيقاف التشغيل في الخلفية
            self.disable_background_playback()
            
            # إلغاء تسجيل معالج الإشعارات
            if self.notification_receiver:
                self.notification_receiver.unregister_receiver()
            
            logger.info("✅ Background playback manager cleaned up")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to cleanup: {e}")
            return False

# إنشاء مثيل عام لمدير التشغيل في الخلفية
background_playback_manager = BackgroundPlaybackManager()
