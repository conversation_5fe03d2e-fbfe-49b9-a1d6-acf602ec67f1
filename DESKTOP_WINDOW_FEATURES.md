# Desktop Window Features - Arabic Music Player

## Overview / نظرة عامة

The Arabic Music Player now supports desktop computers with a mobile-like window interface instead of fullscreen mode.

يدعم مشغل الموسيقى العربي الآن أجهزة الحاسوب المكتبية بواجهة نافذة تشبه الهاتف المحمول بدلاً من وضع ملء الشاشة.

## New Features / الميزات الجديدة

### 🖥️ Desktop Window Configuration / إعدادات نافذة الحاسوب

- **Mobile-like Size**: Window opens with mobile phone dimensions (400x700 pixels)
- **حجم يشبه الهاتف المحمول**: النافذة تفتح بأبعاد الهاتف المحمول (400×700 بكسل)

- **Centered Position**: Window automatically centers on your screen
- **موضع متوسط**: النافذة تتوسط تلقائياً على شاشتك

- **Resizable**: You can resize the window by dragging the edges
- **قابلة لتغيير الحجم**: يمكنك تغيير حجم النافذة بسحب الحواف

- **Size Limits**: Minimum size (350x600) and maximum size (500x900)
- **حدود الحجم**: حد أدنى (350×600) وحد أقصى (500×900)

### ⌨️ Keyboard Shortcuts / اختصارات لوحة المفاتيح

#### Window Size Control / التحكم في حجم النافذة

- **F5** or **Ctrl+R**: Toggle between different window sizes
- **F5** أو **Ctrl+R**: التبديل بين أحجام النافذة المختلفة

- **Ctrl+1**: Small size (350x600)
- **Ctrl+1**: حجم صغير (350×600)

- **Ctrl+2**: Medium size (400x700) - Default
- **Ctrl+2**: حجم متوسط (400×700) - افتراضي

- **Ctrl+3**: Large size (450x800)
- **Ctrl+3**: حجم كبير (450×800)

- **Ctrl+4**: Tablet size (500x900)
- **Ctrl+4**: حجم التابلت (500×900)

- **F11**: Toggle fullscreen mode
- **F11**: تبديل وضع ملء الشاشة

### 📱 Platform Detection / كشف المنصة

- **Android**: Automatically uses fullscreen mode
- **أندرويد**: يستخدم تلقائياً وضع ملء الشاشة

- **Desktop**: Uses windowed mode with mobile-like dimensions
- **الحاسوب المكتبي**: يستخدم وضع النافذة بأبعاد تشبه الهاتف المحمول

## How to Use / كيفية الاستخدام

### Starting the Application / بدء التطبيق

1. Run the application: `python main.py`
2. The window will open in mobile size and center on your screen
3. Use keyboard shortcuts to change window size as needed

1. شغل التطبيق: `python main.py`
2. ستفتح النافذة بحجم الهاتف المحمول وتتوسط على شاشتك
3. استخدم اختصارات لوحة المفاتيح لتغيير حجم النافذة حسب الحاجة

### Changing Window Size / تغيير حجم النافذة

**Method 1: Keyboard Shortcuts / الطريقة الأولى: اختصارات لوحة المفاتيح**
- Press F5 to cycle through sizes automatically
- اضغط F5 للتنقل بين الأحجام تلقائياً

**Method 2: Manual Resize / الطريقة الثانية: تغيير الحجم يدوياً**
- Drag the window edges to resize manually
- اسحب حواف النافذة لتغيير الحجم يدوياً

**Method 3: Specific Size / الطريقة الثالثة: حجم محدد**
- Use Ctrl+1, Ctrl+2, Ctrl+3, or Ctrl+4 for specific sizes
- استخدم Ctrl+1، Ctrl+2، Ctrl+3، أو Ctrl+4 للأحجام المحددة

### Fullscreen Mode / وضع ملء الشاشة

- Press F11 to toggle fullscreen mode on/off
- اضغط F11 لتشغيل/إيقاف وضع ملء الشاشة

## Technical Details / التفاصيل التقنية

### Window Sizes / أحجام النافذة

| Size Name | Dimensions | Arabic Name |
|-----------|------------|-------------|
| Small     | 350 x 600  | صغير        |
| Medium    | 400 x 700  | متوسط       |
| Large     | 450 x 800  | كبير        |
| Tablet    | 500 x 900  | تابلت       |

### Platform Behavior / سلوك المنصة

- **Windows/Linux/macOS**: Windowed mode with mobile dimensions
- **Android**: Fullscreen mode (unchanged)

- **ويندوز/لينكس/ماك**: وضع النافذة بأبعاد الهاتف المحمول
- **أندرويد**: وضع ملء الشاشة (بدون تغيير)

## Benefits / الفوائد

✅ **Better Desktop Experience**: No more fullscreen takeover
✅ **تجربة أفضل للحاسوب المكتبي**: لا مزيد من الاستحواذ على ملء الشاشة

✅ **Mobile-like Interface**: Familiar mobile app experience on desktop
✅ **واجهة تشبه الهاتف المحمول**: تجربة تطبيق هاتف مألوفة على الحاسوب

✅ **Flexible Sizing**: Multiple size options to suit your preference
✅ **حجم مرن**: خيارات أحجام متعددة لتناسب تفضيلك

✅ **Easy Controls**: Simple keyboard shortcuts for quick adjustments
✅ **تحكم سهل**: اختصارات لوحة مفاتيح بسيطة للتعديلات السريعة

✅ **Automatic Centering**: Window positions itself in the center of your screen
✅ **توسيط تلقائي**: النافذة تضع نفسها في وسط شاشتك

## Troubleshooting / استكشاف الأخطاء وإصلاحها

### Window Not Centering / النافذة لا تتوسط

If the window doesn't center automatically, you can manually position it or restart the application.

إذا لم تتوسط النافذة تلقائياً، يمكنك وضعها يدوياً أو إعادة تشغيل التطبيق.

### Keyboard Shortcuts Not Working / اختصارات لوحة المفاتيح لا تعمل

Make sure the application window has focus (click on it) before using keyboard shortcuts.

تأكد من أن نافذة التطبيق لها التركيز (انقر عليها) قبل استخدام اختصارات لوحة المفاتيح.

### Size Limits / حدود الحجم

The window has minimum and maximum size limits for optimal user experience.

النافذة لها حدود حجم أدنى وأقصى لتجربة مستخدم مثلى.

---

**Note**: These features only work on desktop platforms. Android devices will continue to use fullscreen mode automatically.

**ملاحظة**: هذه الميزات تعمل فقط على منصات الحاسوب المكتبي. أجهزة الأندرويد ستستمر في استخدام وضع ملء الشاشة تلقائياً.
