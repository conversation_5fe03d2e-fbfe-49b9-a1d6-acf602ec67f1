"""
محسن السرعة - Speed Optimizer
يوفر تحسينات شاملة لسرعة التطبيق والاستجابة
"""

import time
import threading
import os
import logging
from concurrent.futures import ThreadPoolExecutor
from kivy.clock import Clock
from kivy.cache import Cache

logger = logging.getLogger(__name__)

class SpeedOptimizer:
    """محسن السرعة للتطبيق"""
    
    def __init__(self, app):
        self.app = app
        self.is_active = False
        
        # إعدادات السرعة
        self.settings = {
            'preload_enabled': True,
            'async_loading': True,
            'smart_caching': True,
            'ui_optimization': True,
            'background_processing': True
        }
        
        # إحصائيات السرعة
        self.stats = {
            'startup_time': 0,
            'average_load_time': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'preloaded_items': 0
        }
        
        # خيوط العمل
        self.thread_pool = ThreadPoolExecutor(max_workers=3, thread_name_prefix="SpeedOpt")
        
        # ذاكرة التخزين المؤقت الذكية
        self.smart_cache = {}
        self.preload_queue = []
        
        self._initialize_optimizer()
        logger.info("🚀 Speed Optimizer initialized")

    def _initialize_optimizer(self):
        """تهيئة محسن السرعة"""
        try:
            # تحسين إعدادات Kivy
            self._optimize_kivy_settings()
            
            # تحسين ذاكرة التخزين المؤقت
            self._optimize_cache_settings()
            
            # بدء المعالجة في الخلفية
            self._start_background_processing()
            
            self.is_active = True
            logger.info("✅ Speed optimizer activated")
            
        except Exception as e:
            logger.error(f"❌ Error initializing speed optimizer: {e}")

    def _optimize_kivy_settings(self):
        """تحسين إعدادات Kivy للسرعة"""
        try:
            # تحسين ذاكرة التخزين المؤقت للصور
            Cache.register('kv.image', limit=30, timeout=600)
            Cache.register('kv.texture', limit=25, timeout=300)
            Cache.register('kv.loader', limit=20, timeout=180)
            
            # تحسين إعدادات الرسم
            os.environ.setdefault('KIVY_GL_BACKEND', 'gl')
            os.environ.setdefault('KIVY_WINDOW', 'sdl2')
            
            logger.info("⚡ Kivy settings optimized for speed")
            
        except Exception as e:
            logger.error(f"Error optimizing Kivy settings: {e}")

    def _optimize_cache_settings(self):
        """تحسين إعدادات ذاكرة التخزين المؤقت"""
        try:
            # إنشاء ذاكرة تخزين مؤقت ذكية للبيانات الوصفية
            self.metadata_cache = {}
            
            # إنشاء ذاكرة تخزين مؤقت للصور المحسنة
            self.optimized_image_cache = {}
            
            # إنشاء ذاكرة تخزين مؤقت للأصوات
            self.audio_cache = {}
            
            logger.info("🧠 Smart caching initialized")
            
        except Exception as e:
            logger.error(f"Error optimizing cache: {e}")

    def _start_background_processing(self):
        """بدء المعالجة في الخلفية"""
        try:
            # معالجة دورية للتحميل المسبق
            Clock.schedule_interval(self._process_preload_queue, 2)
            
            # تنظيف دوري للذاكرة المؤقتة
            Clock.schedule_interval(self._cleanup_cache, 30)
            
            logger.info("🔄 Background processing started")
            
        except Exception as e:
            logger.error(f"Error starting background processing: {e}")

    def preload_song_metadata(self, song_path):
        """تحميل مسبق لبيانات الأغنية الوصفية"""
        if not self.settings['preload_enabled']:
            return
            
        try:
            # إضافة إلى قائمة التحميل المسبق
            if song_path not in self.preload_queue:
                self.preload_queue.append(song_path)
                
        except Exception as e:
            logger.debug(f"Error queuing preload: {e}")

    def _process_preload_queue(self, dt):
        """معالجة قائمة التحميل المسبق"""
        if not self.preload_queue:
            return
            
        try:
            # معالجة عنصر واحد في كل مرة لتجنب التحميل الزائد
            song_path = self.preload_queue.pop(0)
            
            # تحميل البيانات الوصفية في خيط منفصل
            self.thread_pool.submit(self._load_metadata_async, song_path)
            
        except Exception as e:
            logger.debug(f"Error processing preload queue: {e}")

    def _load_metadata_async(self, song_path):
        """تحميل البيانات الوصفية بشكل غير متزامن"""
        try:
            if song_path in self.metadata_cache:
                return  # موجود بالفعل
                
            # تحميل البيانات الوصفية
            metadata = self._extract_metadata_fast(song_path)
            
            if metadata:
                self.metadata_cache[song_path] = metadata
                self.stats['preloaded_items'] += 1
                
        except Exception as e:
            logger.debug(f"Error loading metadata async: {e}")

    def _extract_metadata_fast(self, song_path):
        """استخراج سريع للبيانات الوصفية"""
        try:
            # استخراج أساسي سريع
            filename = os.path.basename(song_path)
            title = os.path.splitext(filename)[0]
            
            # محاولة استخراج معلومات إضافية بسرعة
            metadata = {
                'title': title,
                'filename': filename,
                'path': song_path,
                'size': os.path.getsize(song_path) if os.path.exists(song_path) else 0,
                'modified': os.path.getmtime(song_path) if os.path.exists(song_path) else 0
            }
            
            return metadata
            
        except Exception as e:
            logger.debug(f"Error extracting metadata: {e}")
            return None

    def get_cached_metadata(self, song_path):
        """الحصول على البيانات الوصفية من الذاكرة المؤقتة"""
        try:
            if song_path in self.metadata_cache:
                self.stats['cache_hits'] += 1
                return self.metadata_cache[song_path]
            else:
                self.stats['cache_misses'] += 1
                # إضافة إلى قائمة التحميل المسبق للمرة القادمة
                self.preload_song_metadata(song_path)
                return None
                
        except Exception as e:
            logger.debug(f"Error getting cached metadata: {e}")
            return None

    def optimize_ui_update(self, update_func, *args, **kwargs):
        """تحسين تحديث واجهة المستخدم"""
        if not self.settings['ui_optimization']:
            return update_func(*args, **kwargs)
            
        try:
            # تأخير صغير لتجميع التحديثات
            Clock.schedule_once(lambda dt: update_func(*args, **kwargs), 0.05)
            
        except Exception as e:
            logger.debug(f"Error optimizing UI update: {e}")
            update_func(*args, **kwargs)

    def fast_image_load(self, image_path, target_size=None):
        """تحميل سريع للصور"""
        try:
            cache_key = f"{image_path}_{target_size}"
            
            # فحص الذاكرة المؤقتة أولاً
            if cache_key in self.optimized_image_cache:
                self.stats['cache_hits'] += 1
                return self.optimized_image_cache[cache_key]
                
            # تحميل وتحسين الصورة
            if os.path.exists(image_path):
                # تحميل أساسي سريع
                optimized_path = self._optimize_image_fast(image_path, target_size)
                
                if optimized_path:
                    self.optimized_image_cache[cache_key] = optimized_path
                    return optimized_path
                    
            self.stats['cache_misses'] += 1
            return image_path
            
        except Exception as e:
            logger.debug(f"Error in fast image load: {e}")
            return image_path

    def _optimize_image_fast(self, image_path, target_size):
        """تحسين سريع للصورة"""
        try:
            # للآن، إرجاع المسار الأصلي
            # يمكن إضافة تحسينات أكثر تعقيداً لاحقاً
            return image_path
            
        except Exception as e:
            logger.debug(f"Error optimizing image: {e}")
            return image_path

    def batch_process_songs(self, song_list, process_func, batch_size=5):
        """معالجة مجموعية للأغاني"""
        try:
            if not self.settings['background_processing']:
                # معالجة عادية
                for song in song_list:
                    process_func(song)
                return
                
            # معالجة مجموعية
            for i in range(0, len(song_list), batch_size):
                batch = song_list[i:i + batch_size]
                
                # معالجة المجموعة في خيط منفصل
                self.thread_pool.submit(self._process_batch, batch, process_func)
                
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")

    def _process_batch(self, batch, process_func):
        """معالجة مجموعة من الأغاني"""
        try:
            for song in batch:
                process_func(song)
                # تأخير صغير بين العناصر
                time.sleep(0.01)
                
        except Exception as e:
            logger.debug(f"Error processing batch: {e}")

    def _cleanup_cache(self, dt):
        """تنظيف دوري للذاكرة المؤقتة"""
        try:
            current_time = time.time()
            
            # تنظيف البيانات الوصفية القديمة (أكثر من 10 دقائق)
            old_metadata = []
            for path, metadata in self.metadata_cache.items():
                if current_time - metadata.get('loaded_time', 0) > 600:
                    old_metadata.append(path)
                    
            for path in old_metadata:
                del self.metadata_cache[path]
                
            # تنظيف الصور المحسنة القديمة
            if len(self.optimized_image_cache) > 50:
                # إزالة النصف الأقدم
                items = list(self.optimized_image_cache.items())
                for key, _ in items[:len(items)//2]:
                    del self.optimized_image_cache[key]
                    
            logger.debug("🧹 Speed optimizer cache cleaned")
            
        except Exception as e:
            logger.debug(f"Error cleaning cache: {e}")

    def measure_performance(self, operation_name):
        """قياس أداء العمليات"""
        class PerformanceMeasurer:
            def __init__(self, optimizer, name):
                self.optimizer = optimizer
                self.name = name
                self.start_time = None
                
            def __enter__(self):
                self.start_time = time.time()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                if self.start_time:
                    elapsed = time.time() - self.start_time
                    logger.debug(f"⏱️ {self.name}: {elapsed:.3f}s")
                    
                    # تحديث متوسط وقت التحميل
                    if 'load' in self.name.lower():
                        if self.optimizer.stats['average_load_time'] == 0:
                            self.optimizer.stats['average_load_time'] = elapsed
                        else:
                            self.optimizer.stats['average_load_time'] = (
                                self.optimizer.stats['average_load_time'] * 0.8 + elapsed * 0.2
                            )
                            
        return PerformanceMeasurer(self, operation_name)

    def get_speed_report(self):
        """الحصول على تقرير السرعة"""
        try:
            cache_hit_rate = 0
            total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
            if total_requests > 0:
                cache_hit_rate = (self.stats['cache_hits'] / total_requests) * 100
                
            return {
                'status': 'active' if self.is_active else 'inactive',
                'settings': self.settings.copy(),
                'stats': self.stats.copy(),
                'cache_hit_rate': f"{cache_hit_rate:.1f}%",
                'cache_sizes': {
                    'metadata': len(self.metadata_cache),
                    'images': len(self.optimized_image_cache),
                    'preload_queue': len(self.preload_queue)
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating speed report: {e}")
            return {'status': 'error', 'error': str(e)}

    def shutdown(self):
        """إيقاف محسن السرعة"""
        try:
            # إلغاء المهام المجدولة
            Clock.unschedule(self._process_preload_queue)
            Clock.unschedule(self._cleanup_cache)
            
            # إيقاف خيوط العمل
            self.thread_pool.shutdown(wait=False)
            
            # تنظيف الذاكرة المؤقتة
            self.metadata_cache.clear()
            self.optimized_image_cache.clear()
            self.preload_queue.clear()
            
            self.is_active = False
            logger.info("🛑 Speed Optimizer shutdown")
            
        except Exception as e:
            logger.error(f"Shutdown error: {e}")

# دالة مساعدة لتطبيق محسن السرعة
def apply_speed_optimization(app):
    """تطبيق تحسينات السرعة على التطبيق"""
    try:
        if not hasattr(app, 'speed_optimizer'):
            app.speed_optimizer = SpeedOptimizer(app)
            logger.info("🚀 Speed optimization applied to app")
        return app.speed_optimizer
    except Exception as e:
        logger.error(f"Failed to apply speed optimization: {e}")
        return None
