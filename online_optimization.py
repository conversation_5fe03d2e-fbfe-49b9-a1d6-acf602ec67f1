#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تحسين الأداء للأغاني الأونلاين
Online Songs Performance Optimization System

يوفر تحسينات متقدمة لتشغيل الأغاني الأونلاين بسرعة وكفاءة
"""

import time
import threading
import logging
from collections import OrderedDict, deque
from concurrent.futures import ThreadPoolExecutor
import weakref

logger = logging.getLogger(__name__)

class OnlineOptimizer:
    """محسن الأداء للأغاني الأونلاين"""
    
    def __init__(self, max_cache_size=15, max_preload_size=5):
        self.max_cache_size = max_cache_size
        self.max_preload_size = max_preload_size
        
        # نظام التخزين المؤقت المتقدم
        self.url_cache = OrderedDict()  # تخزين روابط الصوت
        self.metadata_cache = OrderedDict()  # تخزين البيانات الوصفية
        self.preload_cache = OrderedDict()  # تخزين التحميل المسبق
        
        # نظام إدارة الخيوط
        self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="OnlineOpt")
        self.active_extractions = {}  # تتبع عمليات الاستخراج النشطة
        
        # إحصائيات الأداء
        self.cache_hits = 0
        self.cache_misses = 0
        self.extraction_times = deque(maxlen=20)
        
        # إعدادات التحسين
        self.enable_preloading = True
        self.enable_background_extraction = True
        self.extraction_timeout = 8  # ثواني
        
        logger.info("🚀 Online optimizer initialized")
    
    def get_cached_url(self, video_id):
        """الحصول على رابط صوتي من التخزين المؤقت"""
        try:
            if video_id in self.url_cache:
                cached_data = self.url_cache[video_id]
                
                # التحقق من انتهاء الصلاحية (25 دقيقة)
                if time.time() - cached_data['timestamp'] < 1500:
                    # نقل إلى النهاية (LRU)
                    self.url_cache.move_to_end(video_id)
                    self.cache_hits += 1
                    logger.debug(f"✅ Cache hit for {video_id}")
                    return cached_data['url']
                else:
                    # إزالة الرابط المنتهي الصلاحية
                    del self.url_cache[video_id]
                    logger.debug(f"🗑️ Removed expired cache for {video_id}")
            
            self.cache_misses += 1
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached URL: {e}")
            return None
    
    def cache_url(self, video_id, url, metadata=None):
        """تخزين رابط صوتي في التخزين المؤقت"""
        try:
            # إزالة العناصر القديمة إذا امتلأ التخزين المؤقت
            while len(self.url_cache) >= self.max_cache_size:
                oldest_key = next(iter(self.url_cache))
                del self.url_cache[oldest_key]
                logger.debug(f"🗑️ Removed old cache entry: {oldest_key}")
            
            # إضافة الرابط الجديد
            self.url_cache[video_id] = {
                'url': url,
                'timestamp': time.time()
            }
            
            # تخزين البيانات الوصفية إذا توفرت
            if metadata:
                self.metadata_cache[video_id] = metadata
            
            logger.debug(f"💾 Cached URL for {video_id}")
            
        except Exception as e:
            logger.error(f"Error caching URL: {e}")
    
    def extract_url_async(self, video_id, youtube_url, callback=None):
        """استخراج رابط الصوت بشكل غير متزامن"""
        try:
            # التحقق من وجود عملية استخراج نشطة
            if video_id in self.active_extractions:
                logger.debug(f"⏳ Extraction already in progress for {video_id}")
                return None
            
            # بدء عملية الاستخراج
            self.active_extractions[video_id] = time.time()
            
            def extraction_task():
                try:
                    start_time = time.time()
                    
                    # استخراج الرابط (يجب تنفيذ هذه الدالة في الكلاس الرئيسي)
                    audio_url = self._extract_audio_url(youtube_url)
                    
                    extraction_time = time.time() - start_time
                    self.extraction_times.append(extraction_time)
                    
                    if audio_url:
                        # تخزين في التخزين المؤقت
                        self.cache_url(video_id, audio_url)
                        logger.debug(f"✅ Extracted URL for {video_id} in {extraction_time:.2f}s")
                        
                        # استدعاء callback إذا توفر
                        if callback:
                            callback(video_id, audio_url, True)
                    else:
                        logger.warning(f"❌ Failed to extract URL for {video_id}")
                        if callback:
                            callback(video_id, None, False)
                    
                except Exception as e:
                    logger.error(f"Error in extraction task: {e}")
                    if callback:
                        callback(video_id, None, False)
                
                finally:
                    # إزالة من العمليات النشطة
                    self.active_extractions.pop(video_id, None)
            
            # تشغيل المهمة في خيط منفصل
            future = self.executor.submit(extraction_task)
            return future
            
        except Exception as e:
            logger.error(f"Error starting async extraction: {e}")
            self.active_extractions.pop(video_id, None)
            return None
    
    def preload_next_songs(self, current_video_id, next_video_ids):
        """تحميل مسبق للأغاني التالية"""
        try:
            if not self.enable_preloading or not next_video_ids:
                return
            
            # تحديد الأغاني التي تحتاج تحميل مسبق
            to_preload = []
            for video_id in next_video_ids[:self.max_preload_size]:
                if video_id != current_video_id and video_id not in self.url_cache:
                    if video_id not in self.preload_cache:
                        to_preload.append(video_id)
            
            if not to_preload:
                return
            
            logger.debug(f"🔄 Preloading {len(to_preload)} songs")
            
            def preload_task(video_id):
                try:
                    youtube_url = f"https://www.youtube.com/watch?v={video_id}"
                    audio_url = self._extract_audio_url(youtube_url)
                    
                    if audio_url:
                        self.preload_cache[video_id] = {
                            'url': audio_url,
                            'timestamp': time.time()
                        }
                        logger.debug(f"✅ Preloaded {video_id}")
                    
                except Exception as e:
                    logger.error(f"Error preloading {video_id}: {e}")
            
            # تشغيل مهام التحميل المسبق
            for video_id in to_preload:
                self.executor.submit(preload_task, video_id)
            
        except Exception as e:
            logger.error(f"Error in preload_next_songs: {e}")
    
    def get_preloaded_url(self, video_id):
        """الحصول على رابط من التحميل المسبق"""
        try:
            if video_id in self.preload_cache:
                preload_data = self.preload_cache[video_id]
                
                # التحقق من الصلاحية
                if time.time() - preload_data['timestamp'] < 1500:  # 25 دقيقة
                    # نقل إلى التخزين المؤقت الرئيسي
                    self.cache_url(video_id, preload_data['url'])
                    del self.preload_cache[video_id]
                    
                    logger.debug(f"✅ Used preloaded URL for {video_id}")
                    return preload_data['url']
                else:
                    # إزالة الرابط المنتهي الصلاحية
                    del self.preload_cache[video_id]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting preloaded URL: {e}")
            return None
    
    def _extract_audio_url(self, youtube_url):
        """استخراج رابط الصوت - يجب تنفيذها في الكلاس الرئيسي"""
        # هذه دالة وهمية - يجب استبدالها بالدالة الحقيقية
        logger.warning("_extract_audio_url not implemented - should be overridden")
        return None
    
    def get_performance_stats(self):
        """الحصول على إحصائيات الأداء"""
        try:
            avg_extraction_time = sum(self.extraction_times) / len(self.extraction_times) if self.extraction_times else 0
            cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
            
            return {
                'cache_size': len(self.url_cache),
                'preload_size': len(self.preload_cache),
                'cache_hits': self.cache_hits,
                'cache_misses': self.cache_misses,
                'cache_hit_rate': cache_hit_rate,
                'avg_extraction_time': avg_extraction_time,
                'active_extractions': len(self.active_extractions)
            }
            
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {}
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            self.executor.shutdown(wait=False)
            self.url_cache.clear()
            self.metadata_cache.clear()
            self.preload_cache.clear()
            self.active_extractions.clear()
            logger.info("🧹 Online optimizer cleaned up")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def __del__(self):
        """تنظيف تلقائي عند حذف الكائن"""
        try:
            self.cleanup()
        except:
            pass
