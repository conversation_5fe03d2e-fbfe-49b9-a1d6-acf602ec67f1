"""
Performance Optimizer for Music Player Application
This module provides performance optimization for the Music Player app.
"""

import os
import time
import logging
import threading
import gc
from kivy.utils import platform
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.core.window import Window

# Configure logging
logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """
    Class to optimize performance of the Music Player application.
    Provides automatic performance tuning based on device capabilities.
    """
    
    def __init__(self, app_instance):
        """
        Initialize the performance optimizer.
        
        Args:
            app_instance: Reference to the main app instance
        """
        self.app = app_instance
        self.is_low_performance = False
        self.is_high_performance = False
        self.memory_usage = 0
        self.cpu_usage = 0
        self.frame_rate = 0
        self.last_frame_time = time.time()
        self.frame_count = 0
        self.fps_samples = []  # Store recent FPS samples for averaging
        self.max_fps_samples = 10  # Keep last 10 samples
        self.optimization_level = 1  # 1=low, 2=medium, 3=high
        self.last_gc_time = time.time()
        self.gc_interval = 60  # Run garbage collection every 60 seconds
        self.image_cache_size = 20  # Maximum number of images to keep in cache
        self.is_foreground = True
        self.last_performance_check = time.time()
        
        # Detect device performance level
        self._detect_performance_level()
        
        # Apply initial optimizations
        self._apply_optimizations()
        
    def start(self):
        """Start the performance optimizer"""
        # Schedule periodic performance checks
        Clock.schedule_interval(self._check_performance, 10)  # Check every 10 seconds

        # Schedule periodic garbage collection
        Clock.schedule_interval(self._run_garbage_collection, self.gc_interval)

        # Monitor frame rate at reasonable frequency
        Clock.schedule_interval(self._monitor_frame_rate, 0.5)  # Check every 500ms

        # Bind to app lifecycle events
        if hasattr(self.app, 'root'):
            Window.bind(on_resize=self._on_window_resize)

        logger.info(f"Performance optimizer started (level: {self.optimization_level})")
        
    def _detect_performance_level(self):
        """Detect the performance level of the device"""
        try:
            # Check platform
            if platform == 'android':
                self._detect_android_performance()
            elif platform == 'ios':
                self._detect_ios_performance()
            else:
                self._detect_desktop_performance()
                
            logger.info(f"Detected performance level: {'Low' if self.is_low_performance else 'High' if self.is_high_performance else 'Medium'}")
        except Exception as e:
            logger.error(f"Error detecting performance level: {e}")
            # Default to medium performance
            self.is_low_performance = False
            self.is_high_performance = False
            
    def _detect_android_performance(self):
        """Detect performance level on Android devices"""
        try:
            # Try to get device info from Android API
            from jnius import autoclass
            Build = autoclass('android.os.Build')
            ActivityManager = autoclass('android.app.ActivityManager')
            Context = autoclass('android.content.Context')
            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            
            # Get memory info
            activity = PythonActivity.mActivity
            am = activity.getSystemService(Context.ACTIVITY_SERVICE)
            mem_info = ActivityManager.MemoryInfo()
            am.getMemoryInfo(mem_info)
            
            # Calculate available memory in MB
            available_mem = mem_info.availMem / 1024 / 1024
            total_mem = mem_info.totalMem / 1024 / 1024
            
            # Check CPU cores
            try:
                with open('/sys/devices/system/cpu/present', 'r') as f:
                    cpu_info = f.read().strip()
                    # Format is typically "0-N" where N+1 is the number of cores
                    if '-' in cpu_info:
                        cores = int(cpu_info.split('-')[1]) + 1
                    else:
                        cores = 1
            except:
                # Default to 4 cores if we can't detect
                cores = 4
                
            # Determine performance level based on memory and cores
            if available_mem < 500 or total_mem < 2000 or cores <= 4:
                self.is_low_performance = True
                self.optimization_level = 3  # High optimization
            elif available_mem > 2000 and total_mem > 4000 and cores >= 8:
                self.is_high_performance = True
                self.optimization_level = 1  # Low optimization
            else:
                self.optimization_level = 2  # Medium optimization
                
            logger.info(f"Android device: {Build.MANUFACTURER} {Build.MODEL}, " +
                       f"Memory: {available_mem:.0f}MB/{total_mem:.0f}MB, Cores: {cores}")
                
        except Exception as e:
            logger.error(f"Error detecting Android performance: {e}")
            # Default to medium performance
            self.optimization_level = 2
            
    def _detect_ios_performance(self):
        """Detect performance level on iOS devices"""
        # Default to medium performance for iOS
        self.optimization_level = 2
            
    def _detect_desktop_performance(self):
        """Detect performance level on desktop platforms"""
        try:
            import psutil
            # Get memory info
            mem = psutil.virtual_memory()
            available_mem = mem.available / 1024 / 1024  # MB
            total_mem = mem.total / 1024 / 1024  # MB
            
            # Get CPU info
            cores = psutil.cpu_count(logical=False)
            if not cores:
                cores = psutil.cpu_count(logical=True)
            
            # Determine performance level
            if available_mem < 1000 or total_mem < 4000 or cores <= 2:
                self.is_low_performance = True
                self.optimization_level = 3  # High optimization
            elif available_mem > 4000 and total_mem > 8000 and cores >= 4:
                self.is_high_performance = True
                self.optimization_level = 1  # Low optimization
            else:
                self.optimization_level = 2  # Medium optimization
                
            logger.info(f"Desktop system: Memory: {available_mem:.0f}MB/{total_mem:.0f}MB, Cores: {cores}")
            
        except ImportError:
            logger.warning("psutil not available, using default performance settings")
            # Default to medium performance
            self.optimization_level = 2
        except Exception as e:
            logger.error(f"Error detecting desktop performance: {e}")
            self.optimization_level = 2
            
    def _apply_optimizations(self):
        """Apply performance optimizations based on detected level"""
        try:
            root = self.app.root
            
            # Apply global optimizations
            if self.is_low_performance:
                # Low performance device optimizations
                self._apply_low_performance_optimizations()
            elif self.is_high_performance:
                # High performance device optimizations
                self._apply_high_performance_optimizations()
            else:
                # Medium performance device optimizations
                self._apply_medium_performance_optimizations()
                
            # Apply common optimizations
            self._apply_common_optimizations()
            
            logger.info(f"Applied performance optimizations (level: {self.optimization_level})")
        except Exception as e:
            logger.error(f"Error applying optimizations: {e}")
            
    def _apply_low_performance_optimizations(self):
        """Apply optimizations for low performance devices"""
        try:
            root = self.app.root
            
            # Reduce animation complexity
            if hasattr(root, 'animation_duration_multiplier'):
                root.animation_duration_multiplier = 1.5  # Slower animations
                
            # Reduce image quality
            self.image_cache_size = 10
            
            # Reduce update frequency
            if hasattr(root, '_progress_timer_interval'):
                root._progress_timer_interval = 0.5  # Update progress less frequently
                
            # Disable cover rotation animation or make it slower
            if hasattr(root, 'start_cover_rotation'):
                # Override the method to use slower rotation
                original_method = root.start_cover_rotation
                def slower_rotation(*args, **kwargs):
                    # Call original with longer duration
                    if 'duration' in kwargs:
                        kwargs['duration'] *= 1.5
                    return original_method(*args, **kwargs)
                root.start_cover_rotation = slower_rotation
                
        except Exception as e:
            logger.error(f"Error applying low performance optimizations: {e}")
            
    def _apply_medium_performance_optimizations(self):
        """Apply optimizations for medium performance devices"""
        try:
            root = self.app.root
            
            # Balanced settings
            if hasattr(root, 'animation_duration_multiplier'):
                root.animation_duration_multiplier = 1.0  # Normal animations
                
            self.image_cache_size = 20
            
            if hasattr(root, '_progress_timer_interval'):
                root._progress_timer_interval = 0.2  # Normal update frequency
                
        except Exception as e:
            logger.error(f"Error applying medium performance optimizations: {e}")
            
    def _apply_high_performance_optimizations(self):
        """Apply optimizations for high performance devices"""
        try:
            root = self.app.root
            
            # Enable all visual effects
            if hasattr(root, 'animation_duration_multiplier'):
                root.animation_duration_multiplier = 0.8  # Faster animations
                
            self.image_cache_size = 30
            
            if hasattr(root, '_progress_timer_interval'):
                root._progress_timer_interval = 0.1  # Frequent updates
                
        except Exception as e:
            logger.error(f"Error applying high performance optimizations: {e}")
            
    def _apply_common_optimizations(self):
        """Apply common optimizations for all devices"""
        try:
            root = self.app.root
            
            # Optimize image loading
            if hasattr(root, '_cover_cache') and isinstance(root._cover_cache, dict):
                # Limit cache size
                if len(root._cover_cache) > self.image_cache_size:
                    # Keep only the most recent entries
                    keys = list(root._cover_cache.keys())
                    for key in keys[:-self.image_cache_size]:
                        del root._cover_cache[key]
                        
            # Optimize title cache
            if hasattr(root, '_title_cache') and isinstance(root._title_cache, dict):
                # Limit cache size
                if len(root._title_cache) > 100:  # Title cache can be larger
                    keys = list(root._title_cache.keys())
                    for key in keys[:-100]:
                        del root._title_cache[key]
                        
        except Exception as e:
            logger.error(f"Error applying common optimizations: {e}")
            
    def _check_performance(self, dt):
        """Periodically check performance and adjust optimizations"""
        try:
            # Only check every 10 seconds to avoid overhead
            current_time = time.time()
            if current_time - self.last_performance_check < 10:
                return
                
            self.last_performance_check = current_time
            
            # Check memory usage
            self._check_memory_usage()
            
            # Check if we need to adjust optimizations
            if self.memory_usage > 80 and self.optimization_level < 3:
                # Memory usage is high, increase optimization
                self.optimization_level += 1
                self._apply_optimizations()
                logger.info(f"Increased optimization level to {self.optimization_level} due to high memory usage")
            elif self.memory_usage < 50 and self.optimization_level > 1 and self.frame_rate > 30:
                # Memory usage is low and frame rate is good, decrease optimization
                self.optimization_level -= 1
                self._apply_optimizations()
                logger.info(f"Decreased optimization level to {self.optimization_level} due to good performance")
                
        except Exception as e:
            logger.error(f"Error checking performance: {e}")
            
    def _check_memory_usage(self):
        """Check current memory usage"""
        try:
            if platform == 'android':
                self._check_android_memory()
            else:
                self._check_desktop_memory()
        except Exception as e:
            logger.error(f"Error checking memory usage: {e}")
            
    def _check_android_memory(self):
        """Check memory usage on Android"""
        try:
            from jnius import autoclass
            ActivityManager = autoclass('android.app.ActivityManager')
            Context = autoclass('android.content.Context')
            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            
            activity = PythonActivity.mActivity
            am = activity.getSystemService(Context.ACTIVITY_SERVICE)
            mem_info = ActivityManager.MemoryInfo()
            am.getMemoryInfo(mem_info)
            
            # Calculate memory usage percentage
            self.memory_usage = 100 - (mem_info.availMem * 100 / mem_info.totalMem)
            
        except Exception as e:
            logger.error(f"Error checking Android memory: {e}")
            self.memory_usage = 50  # Default to 50%
            
    def _check_desktop_memory(self):
        """Check memory usage on desktop platforms"""
        try:
            import psutil
            mem = psutil.virtual_memory()
            self.memory_usage = mem.percent
        except ImportError:
            self.memory_usage = 50  # Default to 50%
        except Exception as e:
            logger.error(f"Error checking desktop memory: {e}")
            self.memory_usage = 50
            
    def _run_garbage_collection(self, dt):
        """Run garbage collection to free memory"""
        try:
            # Run garbage collection
            gc.collect()
            
            # Update last GC time
            self.last_gc_time = time.time()
            
            logger.debug("Ran garbage collection")
        except Exception as e:
            logger.error(f"Error running garbage collection: {e}")
            
    def _monitor_frame_rate(self, dt):
        """Monitor application frame rate using Kivy's Clock dt"""
        try:
            # Use Kivy's dt (delta time) to calculate instantaneous FPS
            if dt > 0:
                instantaneous_fps = 1.0 / dt

                # Add to samples list
                self.fps_samples.append(instantaneous_fps)

                # Keep only recent samples
                if len(self.fps_samples) > self.max_fps_samples:
                    self.fps_samples.pop(0)

                # Calculate average FPS from samples
                if self.fps_samples:
                    self.frame_rate = sum(self.fps_samples) / len(self.fps_samples)
                else:
                    self.frame_rate = 0
            else:
                self.frame_rate = 0

            # Log frame rate periodically (every 30 seconds to reduce spam)
            current_time = time.time()
            if hasattr(self, '_last_fps_log'):
                if current_time - self._last_fps_log >= 30:
                    if self.frame_rate > 0:  # Only log if we have valid FPS
                        logger.debug(f"Performance: {self.frame_rate:.1f} FPS, Memory: {self.memory_usage:.1f}%")
                    self._last_fps_log = current_time
            else:
                self._last_fps_log = current_time

        except Exception as e:
            logger.error(f"Error monitoring frame rate: {e}")
            self.frame_rate = 0
            
    def _on_window_resize(self, instance, width, height):
        """Handle window resize events"""
        try:
            # Apply layout adjustments based on new size
            if hasattr(self.app.root, 'adjust_layout'):
                self.app.root.adjust_layout(None, (width, height))
        except Exception as e:
            logger.error(f"Error handling window resize: {e}")
            
    def log_performance_stats(self):
        """Log current performance statistics"""
        try:
            if self.frame_rate > 0:  # Only log if we have valid data
                logger.info(f"📊 Performance: {self.frame_rate:.1f} FPS | " +
                           f"Memory: {self.memory_usage:.1f}% | " +
                           f"Optimization: Level {self.optimization_level}")
            else:
                logger.info(f"📊 Performance: Initializing... | " +
                           f"Memory: {self.memory_usage:.1f}% | " +
                           f"Optimization: Level {self.optimization_level}")
        except Exception as e:
            logger.error(f"Error logging performance stats: {e}")
