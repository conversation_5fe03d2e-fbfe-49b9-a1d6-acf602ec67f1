#!/usr/bin/env python3
"""
تطبيق اختبار التشغيل في الخلفية
Background playback test app
"""

import os
import logging
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.slider import Slider
from kivy.uix.progressbar import ProgressBar
from kivy.clock import Clock
from kivy.core.audio import SoundLoader
from kivy.core.window import Window

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BackgroundTestWidget(BoxLayout):
    """واجهة اختبار التشغيل في الخلفية"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 20
        self.spacing = 10
        
        # متغيرات التشغيل
        self.current_sound = None
        self.is_playing = False
        self.playlist = []
        self.current_index = 0
        self.background_manager = None
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل مدير التشغيل في الخلفية
        Clock.schedule_once(self.setup_background_manager, 1.0)
        
        # تحميل قائمة الأغاني
        Clock.schedule_once(self.load_music_files, 1.5)
        
        logger.info("BackgroundTestWidget initialized")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        try:
            # عنوان التطبيق
            title = Label(
                text='🎵 اختبار التشغيل في الخلفية\nBackground Playback Test',
                size_hint_y=None,
                height=80,
                font_size=20,
                halign='center'
            )
            self.add_widget(title)
            
            # معلومات الأغنية الحالية
            self.song_label = Label(
                text='لا توجد أغنية محددة\nNo song selected',
                size_hint_y=None,
                height=60,
                font_size=16
            )
            self.add_widget(self.song_label)
            
            # شريط التقدم
            self.progress_bar = ProgressBar(
                max=100,
                value=0,
                size_hint_y=None,
                height=20
            )
            self.add_widget(self.progress_bar)
            
            # أزرار التحكم الأساسية
            controls_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=60,
                spacing=10
            )
            
            prev_btn = Button(text='⏮️ السابق', font_size=14)
            prev_btn.bind(on_press=self.previous_song)
            controls_layout.add_widget(prev_btn)
            
            self.play_btn = Button(text='▶️ تشغيل', font_size=14)
            self.play_btn.bind(on_press=self.toggle_playback)
            controls_layout.add_widget(self.play_btn)
            
            next_btn = Button(text='⏭️ التالي', font_size=14)
            next_btn.bind(on_press=self.next_song)
            controls_layout.add_widget(next_btn)
            
            self.add_widget(controls_layout)
            
            # أزرار التشغيل في الخلفية
            background_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=60,
                spacing=10
            )
            
            self.background_btn = Button(
                text='🔄 تفعيل الخلفية\nEnable Background',
                font_size=12
            )
            self.background_btn.bind(on_press=self.toggle_background_playback)
            background_layout.add_widget(self.background_btn)
            
            self.floating_btn = Button(
                text='🎛️ أزرار عائمة\nFloating Controls',
                font_size=12
            )
            self.floating_btn.bind(on_press=self.toggle_floating_controls)
            background_layout.add_widget(self.floating_btn)
            
            self.add_widget(background_layout)
            
            # أزرار الاختبار
            test_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=60,
                spacing=10
            )
            
            minimize_btn = Button(
                text='📱 تصغير التطبيق\nMinimize App',
                font_size=12
            )
            minimize_btn.bind(on_press=self.minimize_app)
            test_layout.add_widget(minimize_btn)
            
            test_notification_btn = Button(
                text='🔔 اختبار الإشعار\nTest Notification',
                font_size=12
            )
            test_notification_btn.bind(on_press=self.test_notification)
            test_layout.add_widget(test_notification_btn)
            
            self.add_widget(test_layout)
            
            # شريط مستوى الصوت
            volume_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=40,
                spacing=10
            )
            
            volume_label = Label(text='🔊', size_hint_x=None, width=40)
            volume_layout.add_widget(volume_label)
            
            self.volume_slider = Slider(
                min=0, max=1, value=0.7,
                size_hint_y=None, height=40
            )
            self.volume_slider.bind(value=self.on_volume_change)
            volume_layout.add_widget(self.volume_slider)
            
            self.add_widget(volume_layout)
            
            # حالة التطبيق
            self.status_label = Label(
                text='جاري التحضير...\nInitializing...',
                size_hint_y=None,
                height=60,
                font_size=12
            )
            self.add_widget(self.status_label)
            
            # معلومات الخلفية
            self.background_status_label = Label(
                text='حالة الخلفية: غير مفعل\nBackground: Disabled',
                size_hint_y=None,
                height=40,
                font_size=10
            )
            self.add_widget(self.background_status_label)
            
            logger.info("✅ UI setup completed")
            
        except Exception as e:
            logger.error(f"❌ UI setup failed: {e}")
    
    def setup_background_manager(self, dt):
        """إعداد مدير التشغيل في الخلفية"""
        try:
            from notification_receiver import BackgroundPlaybackManager
            
            # إنشاء مدير التشغيل في الخلفية
            self.background_manager = BackgroundPlaybackManager(self.get_app_instance())
            
            self.status_label.text = 'مدير الخلفية جاهز\nBackground manager ready'
            logger.info("✅ Background manager setup completed")
            
        except Exception as e:
            self.status_label.text = f'خطأ في إعداد الخلفية\nBackground setup error: {str(e)[:30]}'
            logger.error(f"❌ Background manager setup failed: {e}")
    
    def get_app_instance(self):
        """الحصول على مثيل التطبيق"""
        try:
            return App.get_running_app()
        except:
            return None
    
    def load_music_files(self, dt):
        """تحميل ملفات الموسيقى"""
        try:
            music_extensions = ['.mp3', '.wav', '.ogg', '.m4a']
            music_dirs = ['music', 'downloads', '.']
            
            self.playlist = []
            
            for music_dir in music_dirs:
                if os.path.exists(music_dir):
                    for file in os.listdir(music_dir):
                        if any(file.lower().endswith(ext) for ext in music_extensions):
                            full_path = os.path.join(music_dir, file)
                            self.playlist.append(full_path)
            
            if self.playlist:
                self.status_label.text = f'تم العثور على {len(self.playlist)} أغنية\nFound {len(self.playlist)} songs'
                self.update_song_info()
            else:
                self.status_label.text = 'لم يتم العثور على أغاني\nNo music files found'
            
            logger.info(f"✅ Loaded {len(self.playlist)} music files")
            
        except Exception as e:
            self.status_label.text = f'خطأ في تحميل الأغاني\nError loading music'
            logger.error(f"❌ Failed to load music files: {e}")
    
    def update_song_info(self):
        """تحديث معلومات الأغنية الحالية"""
        try:
            if self.playlist and 0 <= self.current_index < len(self.playlist):
                current_file = self.playlist[self.current_index]
                filename = os.path.basename(current_file)
                self.song_label.text = f'🎵 {filename}\n({self.current_index + 1}/{len(self.playlist)})'
            else:
                self.song_label.text = 'لا توجد أغنية\nNo song'
        except Exception as e:
            logger.error(f"❌ Failed to update song info: {e}")
    
    def toggle_playback(self, instance):
        """تبديل التشغيل/الإيقاف"""
        try:
            if self.is_playing:
                self.pause_current_song()
            else:
                self.play_current_song()
        except Exception as e:
            logger.error(f"❌ Playback toggle failed: {e}")
    
    def play_current_song(self):
        """تشغيل الأغنية الحالية"""
        try:
            if not self.playlist:
                self.status_label.text = 'لا توجد أغاني للتشغيل\nNo songs to play'
                return
            
            current_file = self.playlist[self.current_index]
            
            # إيقاف التشغيل الحالي
            if self.current_sound:
                self.current_sound.stop()
            
            # تحميل وتشغيل الأغنية
            self.current_sound = SoundLoader.load(current_file)
            
            if self.current_sound:
                self.current_sound.volume = self.volume_slider.value
                self.current_sound.play()
                self.is_playing = True
                self.play_btn.text = '⏸️ إيقاف'
                self.status_label.text = '▶️ يتم التشغيل...\nPlaying...'
                
                # تحديث التشغيل في الخلفية
                if self.background_manager and self.background_manager.is_background_enabled:
                    filename = os.path.basename(current_file)
                    self.background_manager.update_background_info(filename, "مجهول", True)
                
                logger.info(f"✅ Playing: {os.path.basename(current_file)}")
            else:
                self.status_label.text = 'فشل في تحميل الأغنية\nFailed to load song'
                
        except Exception as e:
            self.status_label.text = f'خطأ في التشغيل\nPlayback error'
            logger.error(f"❌ Play failed: {e}")
    
    def pause_current_song(self):
        """إيقاف الأغنية الحالية"""
        try:
            if self.current_sound:
                self.current_sound.stop()
            
            self.is_playing = False
            self.play_btn.text = '▶️ تشغيل'
            self.status_label.text = '⏸️ متوقف\nPaused'
            
            # تحديث التشغيل في الخلفية
            if self.background_manager and self.background_manager.is_background_enabled:
                filename = os.path.basename(self.playlist[self.current_index]) if self.playlist else "أغنية"
                self.background_manager.update_background_info(filename, "مجهول", False)
            
            logger.info("✅ Playback paused")
            
        except Exception as e:
            logger.error(f"❌ Pause failed: {e}")
    
    def next_song(self, instance=None):
        """الأغنية التالية"""
        try:
            if self.playlist:
                self.current_index = (self.current_index + 1) % len(self.playlist)
                self.update_song_info()
                if self.is_playing:
                    self.play_current_song()
        except Exception as e:
            logger.error(f"❌ Next song failed: {e}")
    
    def previous_song(self, instance=None):
        """الأغنية السابقة"""
        try:
            if self.playlist:
                self.current_index = (self.current_index - 1) % len(self.playlist)
                self.update_song_info()
                if self.is_playing:
                    self.play_current_song()
        except Exception as e:
            logger.error(f"❌ Previous song failed: {e}")
    
    def toggle_background_playback(self, instance):
        """تبديل التشغيل في الخلفية"""
        try:
            if not self.background_manager:
                self.status_label.text = 'مدير الخلفية غير متاح\nBackground manager not available'
                return
            
            if self.background_manager.is_background_enabled:
                # إيقاف التشغيل في الخلفية
                success = self.background_manager.disable_background_playback()
                if success:
                    self.background_btn.text = '🔄 تفعيل الخلفية\nEnable Background'
                    self.background_status_label.text = 'حالة الخلفية: غير مفعل\nBackground: Disabled'
            else:
                # تفعيل التشغيل في الخلفية
                filename = os.path.basename(self.playlist[self.current_index]) if self.playlist else "أغنية"
                success = self.background_manager.enable_background_playback(filename, "مجهول")
                if success:
                    self.background_btn.text = '🛑 إيقاف الخلفية\nDisable Background'
                    self.background_status_label.text = 'حالة الخلفية: مفعل\nBackground: Enabled'
            
        except Exception as e:
            self.status_label.text = f'خطأ في الخلفية\nBackground error'
            logger.error(f"❌ Background toggle failed: {e}")
    
    def toggle_floating_controls(self, instance):
        """تبديل أزرار التحكم العائمة"""
        try:
            if not self.background_manager:
                return
            
            if self.background_manager.floating_controls and self.background_manager.floating_controls.is_showing:
                self.background_manager.floating_controls.hide_floating_controls()
                self.floating_btn.text = '🎛️ أزرار عائمة\nShow Floating'
            else:
                success = self.background_manager.show_floating_controls()
                if success:
                    self.floating_btn.text = '❌ إخفاء عائمة\nHide Floating'
            
        except Exception as e:
            logger.error(f"❌ Floating controls toggle failed: {e}")
    
    def minimize_app(self, instance):
        """تصغير التطبيق لاختبار التشغيل في الخلفية"""
        try:
            from kivy.utils import platform
            if platform == 'android':
                from jnius import autoclass
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                activity = PythonActivity.mActivity
                activity.moveTaskToBack(True)
                logger.info("✅ App minimized")
            else:
                self.status_label.text = 'التصغير متاح على الأندرويد فقط\nMinimize available on Android only'
        except Exception as e:
            logger.error(f"❌ Minimize failed: {e}")
    
    def test_notification(self, instance):
        """اختبار الإشعار"""
        try:
            if self.background_manager and self.background_manager.background_service:
                filename = os.path.basename(self.playlist[self.current_index]) if self.playlist else "أغنية اختبار"
                success = self.background_manager.background_service.create_notification(
                    title=filename,
                    text="اختبار الإشعار",
                    is_playing=self.is_playing
                )
                if success:
                    self.status_label.text = 'تم إنشاء الإشعار\nNotification created'
                else:
                    self.status_label.text = 'فشل في إنشاء الإشعار\nNotification failed'
            else:
                self.status_label.text = 'خدمة الإشعارات غير متاحة\nNotification service unavailable'
        except Exception as e:
            logger.error(f"❌ Test notification failed: {e}")
    
    def on_volume_change(self, instance, value):
        """تغيير مستوى الصوت"""
        try:
            if self.current_sound:
                self.current_sound.volume = value
        except Exception as e:
            logger.error(f"❌ Volume change failed: {e}")

class BackgroundTestApp(App):
    """تطبيق اختبار التشغيل في الخلفية"""
    
    def build(self):
        """بناء التطبيق"""
        try:
            Window.clearcolor = (0.1, 0.1, 0.1, 1)
            return BackgroundTestWidget()
        except Exception as e:
            logger.error(f"❌ App build failed: {e}")
            return Label(text=f'خطأ في بناء التطبيق\nApp build error: {e}')
    
    def on_start(self):
        """عند بدء التطبيق"""
        try:
            logger.info("🚀 Background test app started")
            
            # طلب الأذونات المطلوبة
            self.request_permissions()
            
        except Exception as e:
            logger.error(f"❌ App start failed: {e}")
    
    def request_permissions(self):
        """طلب الأذونات المطلوبة"""
        try:
            from kivy.utils import platform
            if platform == 'android':
                from android.permissions import request_permissions, Permission
                
                permissions = [
                    Permission.READ_EXTERNAL_STORAGE,
                    Permission.WRITE_EXTERNAL_STORAGE,
                    Permission.INTERNET,
                    Permission.FOREGROUND_SERVICE,
                    Permission.WAKE_LOCK,
                    Permission.SYSTEM_ALERT_WINDOW
                ]
                
                request_permissions(permissions)
                logger.info("🔐 Permissions requested")
        except Exception as e:
            logger.error(f"❌ Permission request failed: {e}")
    
    def on_stop(self):
        """عند إغلاق التطبيق"""
        try:
            # تنظيف موارد التشغيل في الخلفية
            if hasattr(self.root, 'background_manager') and self.root.background_manager:
                self.root.background_manager.cleanup()
            
            logger.info("✅ Background test app stopped")
        except Exception as e:
            logger.error(f"❌ App stop failed: {e}")

def main():
    """الدالة الرئيسية"""
    logger.info("🎵 Starting Background Playback Test App")
    app = BackgroundTestApp()
    app.run()

if __name__ == '__main__':
    main()
