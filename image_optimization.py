"""
Image Optimization Module for Music Player
Provides advanced image loading, caching, and optimization techniques.
"""

import os
import time
import threading
import logging
from io import BytesIO
from collections import OrderedDict
from kivy.clock import Clock
from kivy.core.image import Image as CoreImage
from kivy.uix.image import AsyncImage

logger = logging.getLogger(__name__)

class ImageOptimizer:
    """Advanced image optimization and caching system"""
    
    def __init__(self, max_cache_size=50, max_memory_mb=100):
        self.max_cache_size = max_cache_size
        self.max_memory_mb = max_memory_mb
        
        # Image caches
        self.image_cache = OrderedDict()  # Full images
        self.thumbnail_cache = OrderedDict()  # Small thumbnails
        self.metadata_cache = OrderedDict()  # Image metadata
        
        # Memory tracking
        self.current_memory_mb = 0
        self.cache_lock = threading.Lock()
        
        # Background processing
        self.processing_queue = []
        self.processing_thread = None
        self.should_process = threading.Event()
        
        # Optimization settings
        self.thumbnail_size = (64, 64)
        self.max_image_size = (400, 400)
        self.jpeg_quality = 85
        
        self._start_background_processor()
    
    def _start_background_processor(self):
        """Start background image processing thread"""
        try:
            self.processing_thread = threading.Thread(
                target=self._background_processing_loop,
                daemon=True
            )
            self.processing_thread.start()
            logger.info("Image background processor started")
        except Exception as e:
            logger.error(f"Error starting image processor: {e}")
    
    def _background_processing_loop(self):
        """Background processing loop for images"""
        while True:
            try:
                if self.should_process.wait(timeout=5):
                    self.should_process.clear()
                    self._process_queue()
                else:
                    # Periodic cleanup
                    self._cleanup_old_cache()
                    
            except Exception as e:
                logger.error(f"Error in background processing: {e}")
                time.sleep(1)
    
    def _process_queue(self):
        """Process queued image operations"""
        try:
            with self.cache_lock:
                queue_copy = self.processing_queue.copy()
                self.processing_queue.clear()
            
            for operation in queue_copy:
                try:
                    self._process_operation(operation)
                except Exception as e:
                    logger.error(f"Error processing operation {operation}: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing queue: {e}")
    
    def _process_operation(self, operation):
        """Process a single image operation"""
        try:
            op_type = operation.get('type')
            
            if op_type == 'preload':
                self._preload_image(operation['path'], operation.get('size'))
            elif op_type == 'optimize':
                self._optimize_image(operation['path'])
            elif op_type == 'thumbnail':
                self._create_thumbnail(operation['path'])
                
        except Exception as e:
            logger.error(f"Error in operation {operation}: {e}")
    
    def load_image_optimized(self, image_path, target_size=None, create_thumbnail=True):
        """Load image with optimization"""
        try:
            if not os.path.exists(image_path):
                return None
            
            # Check cache first
            cache_key = f"{image_path}_{target_size}"
            
            with self.cache_lock:
                if cache_key in self.image_cache:
                    # Move to end (LRU)
                    self.image_cache.move_to_end(cache_key)
                    return self.image_cache[cache_key]
            
            # Load and optimize image
            optimized_image = self._load_and_optimize(image_path, target_size)
            
            if optimized_image:
                # Cache the image
                self._cache_image(cache_key, optimized_image, image_path)
                
                # Queue thumbnail creation if requested
                if create_thumbnail:
                    self._queue_operation({
                        'type': 'thumbnail',
                        'path': image_path
                    })
            
            return optimized_image
            
        except Exception as e:
            logger.error(f"Error loading optimized image {image_path}: {e}")
            return None
    
    def _load_and_optimize(self, image_path, target_size=None):
        """Load and optimize a single image"""
        try:
            # Load image
            image = CoreImage(image_path)
            
            if not image:
                return None
            
            # Get original size
            original_size = image.texture.size
            
            # Determine target size
            if target_size is None:
                target_size = self._calculate_optimal_size(original_size)
            
            # Resize if needed
            if original_size != target_size:
                image = self._resize_image(image, target_size)
            
            return image
            
        except Exception as e:
            logger.error(f"Error optimizing image {image_path}: {e}")
            return None
    
    def _calculate_optimal_size(self, original_size):
        """Calculate optimal size for image"""
        try:
            width, height = original_size
            max_width, max_height = self.max_image_size
            
            # Calculate scale factor
            scale_x = max_width / width if width > max_width else 1
            scale_y = max_height / height if height > max_height else 1
            scale = min(scale_x, scale_y)
            
            if scale < 1:
                return (int(width * scale), int(height * scale))
            
            return original_size
            
        except Exception as e:
            logger.error(f"Error calculating optimal size: {e}")
            return self.max_image_size
    
    def _resize_image(self, image, target_size):
        """Resize image to target size"""
        try:
            # This is a simplified version - in practice you'd use PIL or similar
            # For now, return original image
            return image
            
        except Exception as e:
            logger.error(f"Error resizing image: {e}")
            return image
    
    def _cache_image(self, cache_key, image, image_path):
        """Cache an image with memory management"""
        try:
            with self.cache_lock:
                # Estimate memory usage (rough calculation)
                if hasattr(image, 'texture') and image.texture:
                    width, height = image.texture.size
                    estimated_mb = (width * height * 4) / (1024 * 1024)  # RGBA
                else:
                    estimated_mb = 1  # Default estimate
                
                # Check if we need to free memory
                while (self.current_memory_mb + estimated_mb > self.max_memory_mb and 
                       len(self.image_cache) > 0):
                    self._remove_oldest_image()
                
                # Check cache size limit
                while len(self.image_cache) >= self.max_cache_size:
                    self._remove_oldest_image()
                
                # Add to cache
                self.image_cache[cache_key] = image
                self.current_memory_mb += estimated_mb
                
                # Update metadata
                self.metadata_cache[cache_key] = {
                    'path': image_path,
                    'size_mb': estimated_mb,
                    'access_time': time.time()
                }
                
        except Exception as e:
            logger.error(f"Error caching image: {e}")
    
    def _remove_oldest_image(self):
        """Remove oldest image from cache"""
        try:
            if not self.image_cache:
                return
            
            # Remove oldest (first item in OrderedDict)
            oldest_key = next(iter(self.image_cache))
            del self.image_cache[oldest_key]
            
            # Update memory tracking
            if oldest_key in self.metadata_cache:
                self.current_memory_mb -= self.metadata_cache[oldest_key]['size_mb']
                del self.metadata_cache[oldest_key]
                
        except Exception as e:
            logger.error(f"Error removing oldest image: {e}")
    
    def get_thumbnail(self, image_path):
        """Get or create thumbnail for image"""
        try:
            cache_key = f"thumb_{image_path}"
            
            with self.cache_lock:
                if cache_key in self.thumbnail_cache:
                    self.thumbnail_cache.move_to_end(cache_key)
                    return self.thumbnail_cache[cache_key]
            
            # Queue thumbnail creation
            self._queue_operation({
                'type': 'thumbnail',
                'path': image_path
            })
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting thumbnail: {e}")
            return None
    
    def _create_thumbnail(self, image_path):
        """Create thumbnail for image"""
        try:
            if not os.path.exists(image_path):
                return
            
            cache_key = f"thumb_{image_path}"
            
            # Check if already exists
            with self.cache_lock:
                if cache_key in self.thumbnail_cache:
                    return
            
            # Load and resize to thumbnail size
            thumbnail = self._load_and_optimize(image_path, self.thumbnail_size)
            
            if thumbnail:
                with self.cache_lock:
                    # Limit thumbnail cache size
                    while len(self.thumbnail_cache) >= self.max_cache_size // 2:
                        oldest_key = next(iter(self.thumbnail_cache))
                        del self.thumbnail_cache[oldest_key]
                    
                    self.thumbnail_cache[cache_key] = thumbnail
                    
        except Exception as e:
            logger.error(f"Error creating thumbnail: {e}")
    
    def preload_images(self, image_paths, priority=False):
        """Preload multiple images"""
        try:
            for path in image_paths:
                if os.path.exists(path):
                    operation = {
                        'type': 'preload',
                        'path': path,
                        'size': self.max_image_size
                    }
                    
                    if priority:
                        # Add to front of queue
                        self.processing_queue.insert(0, operation)
                    else:
                        self._queue_operation(operation)
            
            self.should_process.set()
            
        except Exception as e:
            logger.error(f"Error preloading images: {e}")
    
    def _preload_image(self, image_path, target_size=None):
        """Preload a single image"""
        try:
            # Just load it - it will be cached automatically
            self.load_image_optimized(image_path, target_size, create_thumbnail=True)
        except Exception as e:
            logger.error(f"Error preloading image {image_path}: {e}")
    
    def _queue_operation(self, operation):
        """Queue an operation for background processing"""
        try:
            with self.cache_lock:
                self.processing_queue.append(operation)
            self.should_process.set()
        except Exception as e:
            logger.error(f"Error queuing operation: {e}")
    
    def _cleanup_old_cache(self):
        """Clean up old cache entries"""
        try:
            current_time = time.time()
            max_age = 1800  # 30 minutes
            
            with self.cache_lock:
                # Clean image cache
                keys_to_remove = []
                for key, metadata in self.metadata_cache.items():
                    if current_time - metadata['access_time'] > max_age:
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    if key in self.image_cache:
                        del self.image_cache[key]
                        self.current_memory_mb -= self.metadata_cache[key]['size_mb']
                    del self.metadata_cache[key]
                
                # Clean thumbnail cache
                thumb_keys_to_remove = []
                for key in list(self.thumbnail_cache.keys()):
                    # Remove old thumbnails (simplified check)
                    if len(self.thumbnail_cache) > self.max_cache_size // 3:
                        thumb_keys_to_remove.append(key)
                        break
                
                for key in thumb_keys_to_remove:
                    del self.thumbnail_cache[key]
                    
        except Exception as e:
            logger.error(f"Error cleaning up cache: {e}")
    
    def get_cache_stats(self):
        """Get cache statistics"""
        try:
            with self.cache_lock:
                return {
                    'image_cache_size': len(self.image_cache),
                    'thumbnail_cache_size': len(self.thumbnail_cache),
                    'memory_usage_mb': self.current_memory_mb,
                    'max_memory_mb': self.max_memory_mb,
                    'queue_size': len(self.processing_queue)
                }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}
    
    def clear_cache(self):
        """Clear all caches"""
        try:
            with self.cache_lock:
                self.image_cache.clear()
                self.thumbnail_cache.clear()
                self.metadata_cache.clear()
                self.current_memory_mb = 0
                self.processing_queue.clear()
            
            logger.info("Image caches cleared")
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
    
    def optimize_for_device(self, device_type='medium'):
        """Optimize settings for device type"""
        try:
            if device_type == 'low':
                self.max_cache_size = 20
                self.max_memory_mb = 50
                self.max_image_size = (200, 200)
                self.thumbnail_size = (32, 32)
                self.jpeg_quality = 70
            elif device_type == 'high':
                self.max_cache_size = 100
                self.max_memory_mb = 200
                self.max_image_size = (600, 600)
                self.thumbnail_size = (128, 128)
                self.jpeg_quality = 95
            else:  # medium
                self.max_cache_size = 50
                self.max_memory_mb = 100
                self.max_image_size = (400, 400)
                self.thumbnail_size = (64, 64)
                self.jpeg_quality = 85
            
            logger.info(f"Image optimizer configured for {device_type} performance device")
            
        except Exception as e:
            logger.error(f"Error optimizing for device: {e}")


class OptimizedAsyncImage(AsyncImage):
    """Optimized AsyncImage with intelligent loading"""
    
    def __init__(self, image_optimizer=None, **kwargs):
        super().__init__(**kwargs)
        self.image_optimizer = image_optimizer
        self._load_start_time = None
    
    def _load_source(self, *args):
        """Override to use optimized loading"""
        try:
            if self.image_optimizer and self.source:
                self._load_start_time = time.time()
                
                # Try to get optimized image
                optimized_image = self.image_optimizer.load_image_optimized(
                    self.source,
                    target_size=(int(self.width), int(self.height)) if self.width and self.height else None
                )
                
                if optimized_image:
                    self.texture = optimized_image.texture
                    load_time = time.time() - self._load_start_time
                    logger.debug(f"Optimized image loaded in {load_time:.3f}s: {self.source}")
                    return
            
            # Fallback to normal loading
            super()._load_source(*args)
            
        except Exception as e:
            logger.error(f"Error in optimized image loading: {e}")
            super()._load_source(*args)
