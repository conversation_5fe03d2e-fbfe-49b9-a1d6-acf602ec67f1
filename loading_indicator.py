#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مؤشرات التحميل للأغاني
Loading Indicators System for Songs

يوفر مؤشرات بصرية واضحة لحالة تحميل الأغاني
"""

import time
import threading
import logging
from kivy.clock import Clock
from kivy.animation import Animation
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.progressbar import ProgressBar
from kivy.uix.popup import Popup
from kivy.metrics import dp, sp
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.spinner import MDSpinner
from kivymd.uix.button import MDIconButton
from kivymd.toast import toast

logger = logging.getLogger(__name__)

class LoadingIndicator:
    """مؤشر التحميل المتقدم"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.current_popup = None
        self.loading_card = None
        self.progress_bar = None
        self.spinner = None
        self.status_label = None
        self.cancel_button = None
        
        # إعدادات المؤشر
        self.show_progress = True
        self.show_spinner = True
        self.show_cancel_button = True
        self.auto_hide_delay = 0.5  # ثواني بعد اكتمال التحميل
        
        # حالة التحميل
        self.is_loading = False
        self.current_task = None
        self.start_time = 0
        
        logger.info("🔄 Loading indicator system initialized")
    
    def show_loading(self, message="جاري التحميل...", task_type="general", cancelable=True):
        """عرض مؤشر التحميل"""
        try:
            if self.is_loading:
                # تحديث الرسالة إذا كان التحميل جاري
                self.update_message(message)
                return
            
            self.is_loading = True
            self.current_task = task_type
            self.start_time = time.time()
            
            # إنشاء مؤشر التحميل حسب النوع
            if task_type == "song_loading":
                self._create_song_loading_indicator(message, cancelable)
            elif task_type == "online_extraction":
                self._create_online_extraction_indicator(message, cancelable)
            elif task_type == "download":
                self._create_download_indicator(message, cancelable)
            else:
                self._create_general_loading_indicator(message, cancelable)
            
            logger.debug(f"Loading indicator shown: {message}")
            
        except Exception as e:
            logger.error(f"Error showing loading indicator: {e}")
    
    def _create_song_loading_indicator(self, message, cancelable):
        """إنشاء مؤشر تحميل الأغاني"""
        try:
            # إنشاء كارت التحميل
            self.loading_card = MDCard(
                size_hint=(0.8, None),
                height=dp(120),
                pos_hint={'center_x': 0.5, 'center_y': 0.5},
                elevation=8,
                radius=[15],
                md_bg_color=[0.1, 0.1, 0.1, 0.95]
            )
            
            # تخطيط المحتوى
            content_layout = BoxLayout(
                orientation='vertical',
                padding=dp(20),
                spacing=dp(10)
            )
            
            # أيقونة الموسيقى والنص
            header_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(10)
            )
            
            # أيقونة الموسيقى
            music_icon = MDIconButton(
                icon="music-note",
                theme_icon_color="Custom",
                icon_color=[0.3, 0.7, 1, 1],
                icon_size=dp(30)
            )
            header_layout.add_widget(music_icon)
            
            # نص الحالة
            self.status_label = MDLabel(
                text=message,
                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(16),
                theme_text_color="Custom",
                text_color=[1, 1, 1, 1],
                halign="right" if self._is_arabic(message) else "left"
            )
            header_layout.add_widget(self.status_label)
            
            content_layout.add_widget(header_layout)
            
            # شريط التقدم والدوار
            progress_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(30),
                spacing=dp(10)
            )
            
            # دوار التحميل
            self.spinner = MDSpinner(
                size_hint=(None, None),
                size=(dp(25), dp(25)),
                active=True
            )
            progress_layout.add_widget(self.spinner)
            
            # شريط التقدم
            self.progress_bar = ProgressBar(
                max=100,
                value=0,
                size_hint_y=None,
                height=dp(6)
            )
            progress_layout.add_widget(self.progress_bar)
            
            # زر الإلغاء
            if cancelable:
                self.cancel_button = MDIconButton(
                    icon="close",
                    theme_icon_color="Custom",
                    icon_color=[1, 0.3, 0.3, 1],
                    icon_size=dp(20),
                    on_release=self.cancel_loading
                )
                progress_layout.add_widget(self.cancel_button)
            
            content_layout.add_widget(progress_layout)
            self.loading_card.add_widget(content_layout)
            
            # إضافة إلى الشاشة
            if hasattr(self.app, 'root'):
                self.app.root.add_widget(self.loading_card)
            
            # تأثير الظهور
            self.loading_card.opacity = 0
            Animation(opacity=1, duration=0.3).start(self.loading_card)
            
            # بدء تحديث التقدم
            self._start_progress_animation()
            
        except Exception as e:
            logger.error(f"Error creating song loading indicator: {e}")
    
    def _create_online_extraction_indicator(self, message, cancelable):
        """إنشاء مؤشر استخراج الروابط الأونلاين"""
        try:
            # استخدام toast للاستخراج السريع
            toast(f"🔗 {message}")
            
            # إنشاء مؤشر مبسط
            self.loading_card = MDCard(
                size_hint=(0.7, None),
                height=dp(80),
                pos_hint={'center_x': 0.5, 'top': 0.9},
                elevation=6,
                radius=[10],
                md_bg_color=[0.2, 0.6, 1, 0.9]
            )
            
            content_layout = BoxLayout(
                orientation='horizontal',
                padding=dp(15),
                spacing=dp(10)
            )
            
            # دوار صغير
            self.spinner = MDSpinner(
                size_hint=(None, None),
                size=(dp(20), dp(20)),
                active=True
            )
            content_layout.add_widget(self.spinner)
            
            # نص الحالة
            self.status_label = MDLabel(
                text=message,
                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(14),
                theme_text_color="Custom",
                text_color=[1, 1, 1, 1]
            )
            content_layout.add_widget(self.status_label)
            
            self.loading_card.add_widget(content_layout)
            
            if hasattr(self.app, 'root'):
                self.app.root.add_widget(self.loading_card)
            
            # تأثير الانزلاق من الأعلى
            self.loading_card.y = self.app.root.height
            Animation(y=self.app.root.height * 0.8, duration=0.4).start(self.loading_card)
            
        except Exception as e:
            logger.error(f"Error creating online extraction indicator: {e}")
    
    def _create_download_indicator(self, message, cancelable):
        """إنشاء مؤشر التحميل"""
        try:
            # مؤشر تحميل مفصل مع نسبة مئوية
            self.loading_card = MDCard(
                size_hint=(0.85, None),
                height=dp(140),
                pos_hint={'center_x': 0.5, 'center_y': 0.5},
                elevation=10,
                radius=[20],
                md_bg_color=[0.1, 0.5, 0.2, 0.95]
            )
            
            content_layout = BoxLayout(
                orientation='vertical',
                padding=dp(20),
                spacing=dp(15)
            )
            
            # عنوان التحميل
            title_label = MDLabel(
                text="📥 تحميل الأغنية",
                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(18),
                theme_text_color="Custom",
                text_color=[1, 1, 1, 1],
                halign="center",
                size_hint_y=None,
                height=dp(30)
            )
            content_layout.add_widget(title_label)
            
            # نص الحالة
            self.status_label = MDLabel(
                text=message,
                font_name='NotoNaskhArabic-VariableFont_wght',
                font_size=sp(14),
                theme_text_color="Custom",
                text_color=[0.9, 0.9, 0.9, 1],
                halign="center",
                size_hint_y=None,
                height=dp(25)
            )
            content_layout.add_widget(self.status_label)
            
            # شريط التقدم مع نسبة مئوية
            progress_layout = BoxLayout(
                orientation='vertical',
                spacing=dp(5)
            )
            
            self.progress_bar = ProgressBar(
                max=100,
                value=0,
                size_hint_y=None,
                height=dp(8)
            )
            progress_layout.add_widget(self.progress_bar)
            
            # نص النسبة المئوية
            self.percentage_label = MDLabel(
                text="0%",
                font_size=sp(12),
                theme_text_color="Custom",
                text_color=[1, 1, 1, 1],
                halign="center",
                size_hint_y=None,
                height=dp(20)
            )
            progress_layout.add_widget(self.percentage_label)
            
            content_layout.add_widget(progress_layout)
            
            # أزرار التحكم
            if cancelable:
                buttons_layout = BoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(40),
                    spacing=dp(10)
                )
                
                self.cancel_button = MDIconButton(
                    icon="close-circle",
                    theme_icon_color="Custom",
                    icon_color=[1, 0.3, 0.3, 1],
                    on_release=self.cancel_loading
                )
                buttons_layout.add_widget(self.cancel_button)
                
                content_layout.add_widget(buttons_layout)
            
            self.loading_card.add_widget(content_layout)
            
            if hasattr(self.app, 'root'):
                self.app.root.add_widget(self.loading_card)
            
            # تأثير التكبير
            self.loading_card.scale_x = 0.5
            self.loading_card.scale_y = 0.5
            Animation(scale_x=1, scale_y=1, duration=0.4).start(self.loading_card)
            
        except Exception as e:
            logger.error(f"Error creating download indicator: {e}")
    
    def _create_general_loading_indicator(self, message, cancelable):
        """إنشاء مؤشر تحميل عام"""
        try:
            # مؤشر بسيط وسريع
            toast(f"⏳ {message}")
            
        except Exception as e:
            logger.error(f"Error creating general loading indicator: {e}")
    
    def update_progress(self, percentage, message=None):
        """تحديث نسبة التقدم"""
        try:
            if not self.is_loading:
                return
            
            def update_ui(dt):
                if self.progress_bar:
                    self.progress_bar.value = percentage
                
                if hasattr(self, 'percentage_label') and self.percentage_label:
                    self.percentage_label.text = f"{percentage:.0f}%"
                
                if message and self.status_label:
                    self.status_label.text = message
            
            Clock.schedule_once(update_ui, 0)
            
        except Exception as e:
            logger.error(f"Error updating progress: {e}")
    
    def update_message(self, message):
        """تحديث رسالة الحالة"""
        try:
            if self.status_label:
                def update_ui(dt):
                    self.status_label.text = message
                Clock.schedule_once(update_ui, 0)
                
        except Exception as e:
            logger.error(f"Error updating message: {e}")
    
    def hide_loading(self, success=True, final_message=None):
        """إخفاء مؤشر التحميل"""
        try:
            if not self.is_loading:
                return
            
            self.is_loading = False
            
            # عرض رسالة النتيجة النهائية
            if final_message:
                if success:
                    toast(f"✅ {final_message}")
                else:
                    toast(f"❌ {final_message}")
            elif success:
                toast("✅ تم التحميل بنجاح")
            else:
                toast("❌ فشل في التحميل")
            
            # إخفاء المؤشر مع تأثير
            if self.loading_card:
                def hide_card(dt):
                    if self.loading_card and hasattr(self.app, 'root'):
                        # تأثير الاختفاء
                        anim = Animation(opacity=0, duration=0.3)
                        anim.bind(on_complete=lambda *args: self._remove_card())
                        anim.start(self.loading_card)
                
                Clock.schedule_once(hide_card, self.auto_hide_delay)
            
            # إيقاف الدوار
            if self.spinner:
                self.spinner.active = False
            
            # حساب وقت التحميل
            if self.start_time:
                duration = time.time() - self.start_time
                logger.debug(f"Loading completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Error hiding loading indicator: {e}")
    
    def cancel_loading(self, *args):
        """إلغاء التحميل"""
        try:
            self.hide_loading(success=False, final_message="تم إلغاء التحميل")
            
            # إشعار التطبيق بالإلغاء
            if hasattr(self.app, 'cancel_current_loading'):
                self.app.cancel_current_loading()
            
            logger.debug("Loading cancelled by user")
            
        except Exception as e:
            logger.error(f"Error cancelling loading: {e}")
    
    def _remove_card(self):
        """إزالة كارت التحميل"""
        try:
            if self.loading_card and hasattr(self.app, 'root'):
                self.app.root.remove_widget(self.loading_card)
            
            # تنظيف المراجع
            self.loading_card = None
            self.progress_bar = None
            self.spinner = None
            self.status_label = None
            self.cancel_button = None
            
        except Exception as e:
            logger.error(f"Error removing loading card: {e}")
    
    def _start_progress_animation(self):
        """بدء تحريك شريط التقدم"""
        try:
            if not self.progress_bar:
                return
            
            def animate_progress():
                progress = 0
                while self.is_loading and progress < 90:
                    progress += 2
                    Clock.schedule_once(lambda dt: self.update_progress(progress), 0)
                    time.sleep(0.1)
            
            thread = threading.Thread(target=animate_progress, daemon=True)
            thread.start()
            
        except Exception as e:
            logger.error(f"Error starting progress animation: {e}")
    
    def _is_arabic(self, text):
        """التحقق من وجود نص عربي"""
        try:
            return any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in text)
        except:
            return False
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            self.hide_loading(success=False)
            self._remove_card()
            logger.info("🧹 Loading indicator cleaned up")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
